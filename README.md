# COSMIC功能需求文档生成器

基于COSMIC功能点分析方法的自动化功能需求文档生成工具。该工具可以将COSMIC功能拆解数据自动转换为结构化的功能需求文档，包括功能描述、时序图等。

## 项目简介

本项目旨在解决软件开发过程中功能需求文档编写效率低下的问题。通过分析COSMIC功能拆解数据，自动生成标准化的功能需求文档，大大减少需求分析师的工作量，提高文档编写效率和质量一致性。

## 功能特性

- **自动化文档生成**：基于COSMIC功能拆解数据自动生成功能需求文档
- **结构化输出**：按照标准目录结构生成Markdown格式文档
- **时序图生成**：自动为三级模块生成业务时序图（Mermaid语法）
- **批量处理**：支持大批量数据分批次处理，避免大模型处理超限
- **多线程支持**：支持并发处理，提高生成效率
- **知识库集成**：可集成领域知识库，提升生成质量
- **灵活配置**：支持自定义起始编号、批处理大小等参数

## 安装依赖

```
sudo apt install pandoc
pip install playwright -i https://mirrors.aliyun.com/pypi/simple/
playwright install chromium
playwright install-deps
pip install -r requirements.txt  -i https://mirrors.aliyun.com/pypi/simple/
```

## 使用方法

```
python src/web-ui.py
```

## 配置说明
 参见`.env`文件。

## 输出说明

```
功能描述:
  一级模块/二级模块/三级模块
  功能过程名称

时序图:
```

# Docker环境测试

```bash

docker run -ti --rm -v $(pwd):/cosmic-ai -p 5002:5002 python:3.11 bash
# 更新源
tee /etc/apt/sources.list <<'EOF'
deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm          main contrib non-free non-free-firmware
deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-updates main contrib non-free non-free-firmware
deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-backports main contrib non-free non-free-firmware
deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main contrib non-free non-free-firmware
EOF
rm /etc/apt/sources.list.d/*
apt update

apt install -y pandoc
pip install playwright -i https://mirrors.aliyun.com/pypi/simple/
playwright install-deps
playwright install chromium

cd /cosmic-ai/
pip install -r requirements.txt  -i https://mirrors.aliyun.com/pypi/simple/

python src/web_ui.py
```