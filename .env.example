# COSMIC功能拆解系统配置文件

# 基本配置
DATA_DIR='data'
THREAD_COUNT='4'
# 每次LLM处理的子过程数量
BATCH_COUNT='100'
# 文档输出时排除的字段
OUT_EXCLUDED_FIELDS='预估工作量（人天）,功能描述'

# 用于调试的配置，可以配置只拆解某个二级模块或某个三级模块的功能
TEST_LEVEL2_NAME=''
TEST_LEVEL3_NAME=''

# **********大模型相关配置**********
# LLM配置-本地
OPENAI_API_BASE='https://ai.secsign.online:3003/v1'
OPENAI_API_KEY='sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB'
OPENAI_MODEL='qwen3-32b'
# LLM配置-阿里
DASHSCOPE_API_KEY='sk-1d06e75d7fd94338b5b32cf8f9099651'
DASHSCOPE_MODEL='qwen3-coder-plus'
# 仅用于功能拆解的大模型温度参数
LLM_TEMPERATURE='0.6'
API_QPM='600'
API_TPM='10000000'

# 提示词配置
PROMPT_PATH='prompt/prompt.md'

# 嵌入模型配置
EMBEDDING_MODEL='embed'
EMBEDDING_API_BASE='https://ai.secsign.online:38080'
EMBEDDING_API_KEY='sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB'


# **********知识库配置**********
KNOWLEDGE_BASE_ENABLED='true'
KNOWLEDGE_BASE_CACHE_DIR='data/gansu_knowledge'
MARKDOWN_MANUAL_PATH='docs/甘肃移动/SHM1812软件密码模块产品用户手册-外部公开.md:#,docs/甘肃移动/SZT1701数字证书认证系统用户手册v5.0.1.2.md:####,docs/甘肃移动/协同签名服务 用户手册V4.6.8.md:###,docs/甘肃移动/密码软算法技术白皮书-外部公开.md:###,docs/甘肃移动/密码软算法接口文档（C）V1.14.md:###,docs/甘肃移动/密码软算法用户手册-外部公开.md:#,docs/甘肃移动/签名验签使用手册-ccsp-3.4.0:###,docs/河南移动手册/三未信安密码服务平台管理端用户手册v3.4.0(无区域多租户模式).md:####'
SQL_FILE_PATH='docs/V3.4.pdma.json'

# **********功能需求文档生成配置**********
# 文档章节的起始编号
DOC_START_NUMBER='2'
MAX_THREAD_COUNT='4'
LLM_PROVIDER='openai'
SILICONFLOW_MODEL='Qwen/Qwen2-7B-Instruct'
SILICONFLOW_API_KEY=''
KNOWLEDGE_BASE_TOP_K='5'
KNOWLEDGE_BASE_SIMILARITY_THRESHOLD='0.3'
CHUNK_PROCESSING_ENABLED='true'
MARKDOWN_CHILD_PATTERN='####'
SQL_CHUNK_BY_TABLE='true'
MAX_CHUNK_SIZE='20480'
MIN_CHUNK_SIZE='100'
EMBEDDING_BATCH_SIZE='3'
CHECK_OUTPUT_DIR='debug'
