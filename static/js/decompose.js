// 功能拆解页面JavaScript

let decomposeTimer = null;
let decomposeStartTime = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDecomposePage();
    refreshFiles();
});

// 初始化拆解页面
function initializeDecomposePage() {
    // 监听文件选择变化，加载模块选项
    document.getElementById('decomposeFileSelect').addEventListener('change', function() {
        const filename = this.value;
        if (filename) {
            loadModuleOptions(filename);
        } else {
            clearModuleOptions();
        }
    });
}

// 刷新文件列表
function refreshFiles() {
    Promise.all([
        fetch('/api/files').then(r => r.json()),
        fetch('/api/prompts').then(r => r.json())
    ])
    .then(([filesData, promptsData]) => {
        if (filesData.success) {
            updateFileSelects(filesData.uploaded_files || [], filesData.output_files || []);
        }
        if (promptsData.success) {
            updatePromptSelect(promptsData.prompts || []);
        }
    })
    .catch(error => {
        console.error('刷新文件列表失败:', error);
    });
}

// 更新文件选择下拉框
function updateFileSelects(uploadedFiles, outputFiles) {
    const decomposeFileSelect = document.getElementById('decomposeFileSelect');
    const outputFilesList = document.getElementById('outputFilesList');
    const outputFilesCount = document.getElementById('outputFilesCount');
    
    // 更新功能清单文件选择
    decomposeFileSelect.innerHTML = '<option value="">请选择文件...</option>';
    uploadedFiles.forEach(file => {
        const option = document.createElement('option');
        option.value = file.name;
        option.textContent = `${file.name}`;
        decomposeFileSelect.appendChild(option);
    });
    
    // 更新拆解结果文件列表
    outputFilesCount.textContent = outputFiles.length;
    
    if (outputFiles.length === 0) {
        outputFilesList.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="bi bi-inbox display-4"></i>
                <p class="mt-2">暂无拆解结果文件</p>
            </div>
        `;
        return;
    }

    outputFilesList.innerHTML = outputFiles.map(file => `
        <div class="file-item" data-filename="${file.name}">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="mb-1">${file.name}</h6>
                    <small class="text-muted">
                        ${(file.size/1024/1024).toFixed(2)} MB | ${file.modified_str}
                    </small>
                </div>
                <div class="col-md-3">
                    <span class="badge bg-info status-badge">拆解完成</span>
                </div>
                <div class="col-md-3 text-end">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="previewFile('outputs', '${file.name}')">
                            <i class="bi bi-eye"></i> 预览
                        </button>
                        <button class="btn btn-outline-success" onclick="downloadFile('outputs', '${file.name}')">
                            <i class="bi bi-download"></i> 下载
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteFile('outputs', '${file.name}')">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// 更新提示词选择下拉框
function updatePromptSelect(prompts) {
    const promptFileSelect = document.getElementById('promptFileSelect');
    
    promptFileSelect.innerHTML = '';
    prompts.forEach(prompt => {
        const option = document.createElement('option');
        option.value = prompt.path;
        option.textContent = prompt.display_name;
        promptFileSelect.appendChild(option);
    });
}

// 加载模块选项
function loadModuleOptions(filename) {
    const fileSelect = document.getElementById('decomposeFileSelect');
    const selectedOption = fileSelect.querySelector(`option[value="${filename}"]`);
    const folder = 'uploads';
    
    fetch(`/api/modules/${folder}/${encodeURIComponent(filename)}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateModuleSelects(data.level2_modules || [], data.level3_modules || []);
        } else {
            clearModuleOptions();
        }
    })
    .catch(error => {
        console.error('加载模块选项失败:', error);
        clearModuleOptions();
    });
}

// 更新模块选择下拉框
function updateModuleSelects(level2Modules, level3Modules) {
    const level2Select = document.getElementById('level2ModuleSelect');
    const level3Select = document.getElementById('level3ModuleSelect');
    
    // 更新二级模块选择
    level2Select.innerHTML = '<option value="">全部二级模块</option>';
    level2Modules.forEach(module => {
        const option = document.createElement('option');
        option.value = module;
        option.textContent = module;
        level2Select.appendChild(option);
    });
    
    // 更新三级模块选择
    level3Select.innerHTML = '<option value="">全部三级模块</option>';
    level3Modules.forEach(module => {
        const option = document.createElement('option');
        option.value = module;
        option.textContent = module;
        level3Select.appendChild(option);
    });
}

// 清空模块选项
function clearModuleOptions() {
    document.getElementById('level2ModuleSelect').innerHTML = '<option value="">全部二级模块</option>';
    document.getElementById('level3ModuleSelect').innerHTML = '<option value="">全部三级模块</option>';
}

// 开始拆解
function startDecompose() {
    const filename = document.getElementById('decomposeFileSelect').value;
    const promptFile = document.getElementById('promptFileSelect').value;
    const level2Module = document.getElementById('level2ModuleSelect').value;
    const level3Module = document.getElementById('level3ModuleSelect').value;
    
    if (!filename) {
        showMessage('错误', '请选择功能清单文件', 'error');
        return;
    }
    
    if (!promptFile) {
        showMessage('错误', '请选择提示词文件', 'error');
        return;
    }
    
    // 获取文件所在的文件夹
    const fileSelect = document.getElementById('decomposeFileSelect');
    const folder ='uploads';
    
    // 开始计时
    startTimer();
    
    const requestData = {
        filename: filename,
        folder: folder,  // 添加文件夹参数
        prompt_file: promptFile
    };
    
    // 添加模块选择参数
    if (level2Module) {
        requestData.level2_module = level2Module;
    }
    if (level3Module) {
        requestData.level3_module = level3Module;
    }
    
    fetch('/api/cosmic_decompose', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        stopTimer();
        // showMessage 中显示耗时
        
        if (data.success) {
            showMessage('成功', data.message, 'success');
            refreshFiles();
        } else {
            if (data.type === 'duplicate_error') {
                showMessage('数据验证失败', data.message, 'warning');
            } else {
                showMessage('错误', data.error || '拆解失败', 'error');
            }
        }
    })
    .catch(error => {
        stopTimer();
        showMessage('错误', '拆解失败: ' + error.message, 'error');
    });
}

// 开始计时
function startTimer() {
    const timerElement = document.getElementById('decomposeTimer');
    decomposeStartTime = Date.now();
    timerElement.style.display = 'inline-block';
    
    decomposeTimer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - decomposeStartTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        timerElement.innerHTML = `<i class="bi bi-clock me-1"></i>已用时: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

// 停止计时
function stopTimer() {
    if (decomposeTimer) {
        clearInterval(decomposeTimer);
        decomposeTimer = null;
    }
    document.getElementById('decomposeTimer').style.display = 'none';
}

// 预览文件
function previewFile(folder, filename) {
    window.open(`/api/preview/${folder}/${encodeURIComponent(filename)}`, '_blank');
}

// 下载文件
function downloadFile(folder, filename) {
    window.location.href = `/api/download/${folder}/${encodeURIComponent(filename)}`;
}

// 删除文件
function deleteFile(folder, filename) {
    if (!confirm(`确定要删除文件 "${filename}" 吗？`)) {
        return;
    }

    fetch(`/api/delete/${folder}/${encodeURIComponent(filename)}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('成功', '文件删除成功', 'success');
            refreshFiles();
        } else {
            showMessage('错误', data.error || '删除失败', 'error');
        }
    })
    .catch(error => {
        showMessage('错误', '删除失败: ' + error.message, 'error');
    });
}

// 显示消息
function showMessage(title, message, type = 'info') {
    const modal = document.getElementById('messageModal');
    const modalTitle = document.getElementById('messageModalTitle');
    const modalBody = document.getElementById('messageModalBody');
    
    modalTitle.textContent = title;
    modalBody.innerHTML = message.replace(/\n/g, '<br>');
    
    // 根据类型设置样式
    modalTitle.className = 'modal-title';
    if (type === 'error') {
        modalTitle.classList.add('text-danger');
    } else if (type === 'success') {
        modalTitle.classList.add('text-success');
    } else if (type === 'warning') {
        modalTitle.classList.add('text-warning');
    }
    
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}
