// 系统配置页面专用JavaScript

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟加载配置，避免与其他初始化冲突
    setTimeout(() => {
        loadConfig();
    }, 100);
});

// Provider配置相关函数
function updateProviderConfig() {
    const provider = document.getElementById('config_LLM_PROVIDER').value;
    const configs = document.querySelectorAll('.provider-config');
    
    configs.forEach(config => {
        const configProvider = config.getAttribute('data-provider');
        if (configProvider === provider) {
            config.style.display = 'block';
        } else {
            config.style.display = 'none';
        }
    });
}

// 切换密码显示/隐藏
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'bi bi-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'bi bi-eye';
    }
}

// 加载配置
function loadConfig() {
    const container = document.getElementById('configContainer');
    if (!container) return;
    
    container.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载配置...</p>
        </div>
    `;
    
    fetch('/api/config')
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    加载配置失败: ${data.error}
                </div>
            `;
        } else {
            renderConfigForm(data.config_groups);
        }
    })
    .catch(error => {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                网络错误，请重试
            </div>
        `;
        console.error('Load config error:', error);
    });
}

function renderConfigForm(configGroups) {
    const container = document.getElementById('configContainer');
    if (!container) return;
    
    if (!Array.isArray(configGroups)) {
        console.error('configGroups is not an array:', configGroups);
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                配置数据格式错误，请刷新页面重试
            </div>
        `;
        return;
    }
    
    let html = '';
    
    configGroups.forEach(group => {
        if (!group.configs || !Array.isArray(group.configs)) {
            console.warn('Group configs is not an array:', group);
            return;
        }
        
        html += `
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">${group.title || '未命名配置组'}</h5>
                    ${group.description ? `<small class="text-muted">${group.description}</small>` : ''}
                </div>
                <div class="card-body">
                    <div class="row">
        `;
        
        group.configs.forEach(config => {
            if (!config.key) {
                console.warn('Config item missing key:', config);
                return;
            }
            
            const colClass = config.type === 'textarea' ? 'col-12' : 'col-md-6';
            html += `<div class="${colClass} mb-3">`;
            
            html += `<label class="form-label" for="config_${config.key}">${config.display_name || config.key}</label>`;
            
            if (config.description) {
                html += `<div class="form-text mb-2">${config.description}</div>`;
            }
            
            if (config.type === 'select') {
                html += `<select class="form-select" id="config_${config.key}" onchange="updateProviderConfig()">`;
                if (config.options && Array.isArray(config.options)) {
                    config.options.forEach(option => {
                        const optionValue = typeof option === 'string' ? option : option.value;
                        const optionLabel = typeof option === 'string' ? option : option.label;
                        const selected = optionValue === config.value ? 'selected' : '';
                        html += `<option value="${optionValue}" ${selected}>${optionLabel}</option>`;
                    });
                }
                html += `</select>`;
            } else if (config.type === 'textarea') {
                html += `<textarea class="form-control" id="config_${config.key}" rows="3">${config.value || ''}</textarea>`;
            } else if (config.type === 'password') {
                html += `
                    <div class="input-group">
                        <input type="password" class="form-control" id="config_${config.key}" value="${config.value || ''}">
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('config_${config.key}')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                `;
            } else if (config.type === 'boolean') {
                const checked = config.value === true || config.value === 'true' ? 'checked' : '';
                html += `<input type="checkbox" class="form-check-input" id="config_${config.key}" ${checked}>`;
            } else {
                const inputType = config.type === 'number' ? 'number' : 'text';
                let inputAttrs = `type="${inputType}" class="form-control" id="config_${config.key}" value="${config.value || ''}"`;
                
                if (config.type === 'number') {
                    if (config.min !== undefined) inputAttrs += ` min="${config.min}"`;
                    if (config.max !== undefined) inputAttrs += ` max="${config.max}"`;
                    if (config.step !== undefined) inputAttrs += ` step="${config.step}"`;
                }
                
                html += `<input ${inputAttrs}>`;
            }
            
            html += `</div>`;
        });
        
        html += `
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
    
    // 初始化Provider配置显示
    updateProviderConfig();
}

// 保存配置
function saveConfig() {
    if (!confirm('确定要保存配置吗？修改后需要重启系统才能生效。')) {
        return;
    }
    
    const configs = {};
    const inputs = document.querySelectorAll('#configContainer input, #configContainer select, #configContainer textarea');
    
    inputs.forEach(input => {
        const key = input.id.replace('config_', '');
        let value;
        
        if (input.type === 'checkbox') {
            value = input.checked;
        } else if (input.type === 'number') {
            value = input.value === '' ? null : parseFloat(input.value);
        } else {
            value = input.value;
        }
        
        configs[key] = value;
    });
    
    fetch('/api/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            configs: configs
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage('保存失败', data.error, 'error');
        } else {
            showMessage('保存成功', '配置已保存，请重启系统使配置生效', 'success');
        }
    })
    .catch(error => {
        showMessage('保存失败', '网络错误，请重试', 'error');
        console.error('Save config error:', error);
    });
}

// 导出配置
function exportConfig() {
    fetch('/api/config/export')
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage('导出失败', data.error, 'error');
        } else {
            // 创建下载链接
            const blob = new Blob([data.content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'config.env';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            showMessage('导出成功', '配置文件已下载', 'success');
        }
    })
    .catch(error => {
        showMessage('导出失败', '网络错误，请重试', 'error');
        console.error('Export config error:', error);
    });
}

// 导入配置
function importConfig() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.env,text/plain';
    
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            const content = e.target.result;
            
            // 显示导入模态框
            const modal = document.getElementById('importConfigModal');
            const textarea = document.getElementById('importConfigContent');
            textarea.value = content;
            
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        };
        reader.readAsText(file);
    };
    
    input.click();
}

// 确认导入配置
function confirmImportConfig() {
    const content = document.getElementById('importConfigContent').value;
    
    if (!content.trim()) {
        showMessage('内容为空', '请输入配置内容', 'warning');
        return;
    }
    
    fetch('/api/config/import', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            content: content
        })
    })
    .then(response => response.json())
    .then(data => {
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('importConfigModal'));
        modal.hide();
        
        if (data.error) {
            showMessage('导入失败', data.error, 'error');
        } else {
            showMessage('导入成功', '配置已导入，正在重新加载...', 'success');
            // 重新加载配置
            setTimeout(() => {
                loadConfig();
            }, 1000);
        }
    })
    .catch(error => {
        showMessage('导入失败', '网络错误，请重试', 'error');
        console.error('Import config error:', error);
    });
}
