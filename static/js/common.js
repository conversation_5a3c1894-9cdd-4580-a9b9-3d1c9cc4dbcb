// COSMIC功能拆解系统通用JavaScript功能

// 全局变量
let timerInterval = null;
let startTime = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeCommon();
});

// 初始化通用功能
function initializeCommon() {
    // 初始化文件上传（如果存在）
    initializeFileUpload();
    
    // 初始化提示词编辑器（如果存在）
    // initializePromptEditor();
    
    // 如果是配置页面，自动加载配置
    if (window.location.pathname === '/config') {
        setTimeout(() => {
            loadConfig();
        }, 100);
    }
}

// 初始化文件上传
function initializeFileUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    
    if (!uploadArea || !fileInput) return;
    
    // 点击上传区域（但不包括按钮）
    uploadArea.addEventListener('click', function(e) {
        // 如果点击的是按钮，不处理（避免重复触发）
        if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
            return;
        }
        fileInput.click();
    });
    
    // 文件选择
    fileInput.addEventListener('change', function() {
        const files = this.files;
        if (files.length > 0) {
            uploadFile(files[0]);
        }
    });
    
    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            uploadFile(files[0]);
        }
    });
}

// 上传文件
function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = progressDiv.querySelector('.progress-bar');
    
    progressDiv.style.display = 'block';
    progressBar.style.width = '0%';
    
    fetch('/api/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        progressDiv.style.display = 'none';
        if (data.error) {
            showMessage('上传失败', data.error, 'error');
        } else {
            showMessage('上传成功', `文件 "${data.filename}" 上传成功`, 'success');
            // 刷新页面以显示新上传的文件
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
    })
    .catch(error => {
        progressDiv.style.display = 'none';
        showMessage('上传失败', '网络错误，请重试', 'error');
        console.error('Upload error:', error);
    });
}

// 预览文件
function previewFile(folder, filename) {
    const url = `/api/preview/${folder}/${filename}`;
    window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
}

// 下载文件
function downloadFile(folder, filename) {
    const url = `/api/download/${folder}/${filename}`;
    window.open(url, '_blank');
}

// 删除文件
function deleteFile(folder, filename) {
    if (!confirm(`确定要删除文件 "${filename}" 吗？`)) {
        return;
    }
    
    fetch(`/api/delete/${folder}/${filename}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage('删除失败', data.error, 'error');
        } else {
            showMessage('删除成功', `文件 "${filename}" 已删除`, 'success');
            // 移除对应的文件项
            const fileItem = document.querySelector(`[data-filename="${filename}"]`);
            if (fileItem) {
                fileItem.remove();
            }
        }
    })
    .catch(error => {
        showMessage('删除失败', '网络错误，请重试', 'error');
        console.error('Delete error:', error);
    });
}

// 刷新文件列表
function refreshFiles() {
    location.reload();
}

// 计时器相关函数
function startTimer(elementId) {
    startTime = Date.now();
    const element = document.getElementById(elementId);
    if (!element) return;
    
    element.style.display = 'block';
    
    timerInterval = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        element.textContent = `已用时: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

function stopTimer(elementId) {
    if (timerInterval) {
        clearInterval(timerInterval);
        timerInterval = null;
    }
    
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'none';
    }
}

// 显示消息
function showMessage(title, message, type = 'info') {
    const modal = document.getElementById('messageModal');
    const modalTitle = document.getElementById('messageModalTitle');
    const modalBody = document.getElementById('messageModalBody');
    
    modalTitle.textContent = title;
    modalBody.textContent = message;
    
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

// 工具函数：格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函数：格式化日期
function formatDate(timestamp) {
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('zh-CN');
}

// 开始生成文档
function startGenerateDocument() {
    const fileSelect = document.getElementById('documentFileSelect');
    const filename = fileSelect.value;

    if (!filename) {
        showMessage('请选择文件', '请先选择要生成文档的拆解结果文件', 'warning');
        return;
    }

    startTimer('documentTimer');

    fetch('/api/generate_document', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            filename: filename
        })
    })
    .then(response => response.json())
    .then(data => {
        stopTimer('documentTimer');
        if (data.error) {
            showMessage('文档生成失败', data.error, 'error');
        } else {
            showMessage('文档生成成功', `文档 "${data.output_file}" 生成完成`, 'success');
            // 刷新页面以显示新生成的文档
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
    })
    .catch(error => {
        stopTimer('documentTimer');
        showMessage('文档生成失败', '网络错误，请重试', 'error');
        console.error('Generate document error:', error);
    });
}

// 转换为Word文档
function convertToWord(filename) {
    if (!confirm(`确定要将 "${filename}" 转换为Word文档吗？`)) {
        return;
    }

    fetch('/api/convert_to_word', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            filename: filename
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage('转换失败', data.error, 'error');
        } else {
            showMessage('转换成功', `Word文档 "${data.output_file}" 生成完成`, 'success');
            // 刷新页面以显示新生成的Word文档
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
    })
    .catch(error => {
        showMessage('转换失败', '网络错误，请重试', 'error');
        console.error('Convert to word error:', error);
    });
}
