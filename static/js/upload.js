// 功能清单管理页面JavaScript

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeUploadPage();
    refreshFiles();
});

// 初始化上传页面
function initializeUploadPage() {
    const fileInput = document.getElementById('fileInput');

    // 文件选择处理
    fileInput.addEventListener('change', function(e) {
        //console.log('文件选择事件被触发，时间:', new Date().toISOString());
        if (e.target.files.length > 0) {
            //console.log('选择的文件:', e.target.files[0].name);
            handleFileUpload(e.target.files[0]);
            // 清空文件输入框，避免重复选择同一文件时不会触发change事件
            e.target.value = '';
        }
    });
}

// 选择文件函数
function selectFile(event) {
    console.log('selectFile 被调用，时间:', new Date().toISOString());
    // 阻止事件冒泡到父元素
    event.stopPropagation();
    document.getElementById('fileInput').click();
}

// 处理文件上传
function handleFileUpload(file) {
    //console.log('handleFileUpload 被调用，文件名:', file.name, '时间:', new Date().toISOString());
    
    // 验证文件类型
    const allowedTypes = ['.xlsx', '.xls'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
        showMessage('错误', '只支持 .xlsx 和 .xls 格式的文件', 'error');
        return;
    }

    // 验证文件大小 (16MB)
    if (file.size > 16 * 1024 * 1024) {
        showMessage('错误', '文件大小不能超过 16MB', 'error');
        return;
    }

    // 显示上传进度
    const progressContainer = document.getElementById('uploadProgress');
    const progressBar = progressContainer.querySelector('.progress-bar');
    progressContainer.style.display = 'block';
    progressBar.style.width = '0%';

    // 创建FormData
    const formData = new FormData();
    formData.append('file', file);

    // 发送上传请求
    fetch('/api/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        progressContainer.style.display = 'none';
        
        if (data.success) {
            if (data.validation && !data.validation.valid) {
                showMessage('上传成功但格式验证失败', data.validation.message, 'warning');
            } else {
                const fileTypeText = data.file_type === 'functional_decompose' ? '功能拆解文件' : '功能清单文件';
                const folderText = data.folder === 'outputs' ? '功能拆解目录' : '功能清单目录';
                
                // 检查文件名是否发生了变化
                let filenameInfo = '';
                if (data.original_filename && data.original_filename !== data.filename) {
                    filenameInfo = `<br><small class="text-muted">文件名已从 "${data.original_filename}" 调整为 "${data.filename}"</small>`;
                }
                
                showMessage('成功', `文件上传成功！<br>文件类型：${fileTypeText}<br>上传目录：${folderText}${filenameInfo}`, 'success');
            }
            refreshFiles();
        } else {
            showMessage('错误', data.error || '上传失败', 'error');
        }
    })
    .catch(error => {
        progressContainer.style.display = 'none';
        showMessage('错误', '上传失败: ' + error.message, 'error');
    });

    // 模拟上传进度
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';
    }, 200);

    // 清理进度条
    setTimeout(() => {
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
    }, 2000);
}

// 刷新文件列表
function refreshFiles() {
    fetch('/api/files')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateUploadedFilesList(data.uploaded_files || []);
        }
    })
    .catch(error => {
        console.error('刷新文件列表失败:', error);
    });
}

// 更新已上传文件列表
function updateUploadedFilesList(files) {
    const filesList = document.getElementById('uploadedFilesList');
    const filesCount = document.getElementById('uploadedFilesCount');
    
    filesCount.textContent = files.length;
    
    if (files.length === 0) {
        filesList.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="bi bi-inbox display-4"></i>
                <p class="mt-2">暂无上传文件</p>
            </div>
        `;
        return;
    }

    filesList.innerHTML = files.map(file => {
        // 根据文件类型设置样式和图标
        let typeClass = '';
        let typeIcon = '';
        if (file.type === 'functional_decompose') {
            typeClass = 'bg-success';
            typeIcon = 'bi-diagram-3';
        } else {
            typeClass = 'bg-primary';
            typeIcon = 'bi-list-check';
        }
        
        return `
        <div class="file-item" data-filename="${file.name}">
            <div class="row align-items-center">
                <div class="col-md-5">
                    <h6 class="mb-1">${file.name}</h6>
                    <div class="d-flex align-items-center">
                        <small class="text-muted">
                            ${(file.size/1024/1024).toFixed(2)} MB | ${file.modified_str}
                        </small>
                    </div>
                </div>
                <div class="col-md-3">
                    <span class="badge bg-secondary status-badge">
                        <i class="bi ${typeIcon} me-1"></i>${file.folder === 'outputs' ? '功能拆解' : '功能清单'}
                    </span>
                </div>
                <div class="col-md-4 text-end">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="previewFile('${file.folder}', '${file.name}')">
                            <i class="bi bi-eye"></i> 预览
                        </button>
                        <button class="btn btn-outline-success" onclick="downloadFile('${file.folder}', '${file.name}')">
                            <i class="bi bi-download"></i> 下载
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteFile('${file.folder}', '${file.name}')">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
        `;
    }).join('');
}

// 预览文件
function previewFile(folder, filename) {
    window.open(`/api/preview/${folder}/${encodeURIComponent(filename)}`, '_blank');
}

// 下载文件
function downloadFile(folder, filename) {
    window.location.href = `/api/download/${folder}/${encodeURIComponent(filename)}`;
}

// 删除文件
function deleteFile(folder, filename) {
    if (!confirm(`确定要删除文件 "${filename}" 吗？`)) {
        return;
    }

    fetch(`/api/delete/${folder}/${encodeURIComponent(filename)}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('成功', '文件删除成功', 'success');
            refreshFiles();
        } else {
            showMessage('错误', data.error || '删除失败', 'error');
        }
    })
    .catch(error => {
        showMessage('错误', '删除失败: ' + error.message, 'error');
    });
}

// 显示消息
function showMessage(title, message, type = 'info') {
    const modal = document.getElementById('messageModal');
    const modalTitle = document.getElementById('messageModalTitle');
    const modalBody = document.getElementById('messageModalBody');
    
    modalTitle.textContent = title;
    modalBody.innerHTML = message.replace(/\n/g, '<br>');
    
    // 根据类型设置样式
    modalTitle.className = 'modal-title';
    if (type === 'error') {
        modalTitle.classList.add('text-danger');
    } else if (type === 'success') {
        modalTitle.classList.add('text-success');
    } else if (type === 'warning') {
        modalTitle.classList.add('text-warning');
    }
    
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}
