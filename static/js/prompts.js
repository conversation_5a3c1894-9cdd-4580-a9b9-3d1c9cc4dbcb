// 提示词管理页面专用JavaScript

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePromptEditor();
});

// 初始化提示词编辑器
function initializePromptEditor() {
    const editMode = document.getElementById('editMode');
    const previewMode = document.getElementById('previewMode');
    const editArea = document.getElementById('promptEditArea');
    const previewArea = document.getElementById('promptPreviewArea');
    const textarea = document.getElementById('promptContent');
    
    if (!editMode || !previewMode || !editArea || !previewArea || !textarea) return;

    // 切换到编辑模式
    editMode.addEventListener('change', function() {
        if (this.checked) {
            editArea.style.display = 'block';
            previewArea.style.display = 'none';
        }
    });

    // 切换到预览模式
    previewMode.addEventListener('change', function() {
        if (this.checked) {
            editArea.style.display = 'none';
            previewArea.style.display = 'block';
            updatePreview();
        }
    });

    // 更新预览内容
    function updatePreview() {
        const content = textarea.value;
        if (!content.trim()) {
            document.getElementById('promptPreviewContent').innerHTML = '<p class="text-muted">请先编辑内容，然后切换到预览模式查看效果</p>';
            return;
        }

        // 检查是否包含Mermaid图表
        if (content.includes('```mermaid')) {
            showMermaidPreview(content);
        } else {
            // 使用marked.js渲染Markdown（如果可用）
            if (typeof marked !== 'undefined') {
                const html = marked.parse(content);
                document.getElementById('promptPreviewContent').innerHTML = html;
            } else {
                // 简单的文本预览
                const html = content.replace(/\n/g, '<br>');
                document.getElementById('promptPreviewContent').innerHTML = html;
            }
        }
    }

    // 显示包含Mermaid的预览
    function showMermaidPreview(content) {
        // 创建一个新窗口来显示完整的Mermaid预览
        const previewWindow = window.open('', '_blank', 'width=1000,height=800,scrollbars=yes');
        
        const html = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>提示词预览</title>
            <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div id="content"></div>
            <script>
                mermaid.initialize({ startOnLoad: true });
                const content = ${JSON.stringify(content)};
                document.getElementById('content').innerHTML = content.replace(/\\n/g, '<br>');
            </script>
        </body>
        </html>
        `;
        
        previewWindow.document.write(html);
        previewWindow.document.close();
    }
}

// 加载提示词内容
function loadPromptContent() {
    const select = document.getElementById('promptEditSelect');
    const textarea = document.getElementById('promptContent');
    const saveAsBtn = document.getElementById('saveAsBtn');
    
    if (!select.value) {
        textarea.value = '';
        textarea.placeholder = '请选择提示词文件后编辑内容...';
        saveAsBtn.disabled = true;
        return;
    }
    
    const isSystem = select.options[select.selectedIndex].getAttribute('data-is-system') === 'true';
    saveAsBtn.disabled = false;
    
    fetch(`/api/prompt/${encodeURIComponent(select.value)}`)
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage('加载失败', data.error, 'error');
            textarea.value = '';
        } else {
            textarea.value = data.content;
            textarea.placeholder = isSystem ? '系统提示词文件，建议另存为新文件后编辑' : '编辑提示词内容...';
        }
    })
    .catch(error => {
        showMessage('加载失败', '网络错误，请重试', 'error');
        console.error('Load prompt error:', error);
    });
}

// 保存提示词内容
function savePromptContent() {
    const select = document.getElementById('promptEditSelect');
    const textarea = document.getElementById('promptContent');
    
    if (!select.value) {
        showMessage('请选择文件', '请先选择要保存的提示词文件', 'warning');
        return;
    }
    
    const isSystem = select.options[select.selectedIndex].getAttribute('data-is-system') === 'true';
    
    if (isSystem && !confirm('这是系统提示词文件，直接修改可能影响系统功能。建议使用"另存为"功能创建新文件。确定要继续吗？')) {
        return;
    }
    
    const content = textarea.value;
    
    fetch(`/api/prompt/${encodeURIComponent(select.value)}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            content: content
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage('保存失败', data.error, 'error');
        } else {
            showMessage('保存成功', '提示词内容已保存', 'success');
        }
    })
    .catch(error => {
        showMessage('保存失败', '网络错误，请重试', 'error');
        console.error('Save prompt error:', error);
    });
}

// 另存提示词内容
function savePromptAs() {
    const textarea = document.getElementById('promptContent');
    const content = textarea.value;
    
    if (!content.trim()) {
        showMessage('内容为空', '请先编辑提示词内容', 'warning');
        return;
    }
    
    const filename = prompt('请输入新文件名（不需要扩展名）：');
    if (!filename) return;
    
    // 验证文件名
    if (!/^[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/.test(filename)) {
        showMessage('文件名无效', '文件名只能包含字母、数字、下划线、连字符和中文字符', 'error');
        return;
    }
    
    fetch('/api/prompt/save-as', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            filename: filename,
            content: content
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage('另存失败', data.error, 'error');
        } else {
            showMessage('另存成功', `文件 "${data.filename}" 已创建`, 'success');
            // 刷新页面以显示新文件
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
    })
    .catch(error => {
        showMessage('另存失败', '网络错误，请重试', 'error');
        console.error('Save as prompt error:', error);
    });
}

// 重置提示词内容
function resetPromptContent() {
    if (!confirm('确定要重置内容吗？未保存的修改将丢失。')) {
        return;
    }
    
    loadPromptContent();
}
