// 检查修复页面JavaScript

let repairTimer = null;
let repairStartTime = null;
let currentRepairResult = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeValidatePage();
    refreshFiles();
});

// 初始化检查修复页面
function initializeValidatePage() {
    // 监听操作模式变化
    document.querySelectorAll('input[name="operationMode"]').forEach(radio => {
        radio.addEventListener('change', function() {
            updateCheckboxStates();
        });
    });
}

// 更新复选框状态
function updateCheckboxStates() {
    const isCheckOnly = document.getElementById('checkOnly').checked;
    const checkboxes = document.querySelectorAll('.repair-options input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
        checkbox.disabled = false; // 所有模式下都可以选择检查项目
    });
}

// 刷新文件列表
function refreshFiles() {
    fetch('/api/files')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateFileSelects(data.output_files || []);
            updateRepairFilesList(data.output_files || []);
        }
    })
    .catch(error => {
        console.error('刷新文件列表失败:', error);
    });
}

// 更新文件选择下拉框
function updateFileSelects(outputFiles) {
    const validateFileSelect = document.getElementById('validateFileSelect');
    
    validateFileSelect.innerHTML = '<option value="">请选择文件...</option>';
    outputFiles.forEach(file => {
        const option = document.createElement('option');
        option.value = file.name;
        option.textContent = file.name;
        validateFileSelect.appendChild(option);
    });
}

// 更新修复结果文件列表
function updateRepairFilesList(outputFiles) {
    const repairFilesList = document.getElementById('repairFilesList');
    const repairFilesCount = document.getElementById('repairFilesCount');
    
    // 筛选出修复文件
    const repairFiles = outputFiles.filter(file => file.name.includes('_修复_'));
    repairFilesCount.textContent = repairFiles.length;
    
    if (repairFiles.length === 0) {
        repairFilesList.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="bi bi-inbox display-4"></i>
                <p class="mt-2">暂无修复结果文件</p>
            </div>
        `;
        return;
    }

    repairFilesList.innerHTML = repairFiles.map(file => `
        <div class="file-item" data-filename="${file.name}">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="mb-1">${file.name}</h6>
                    <small class="text-muted">
                        ${(file.size/1024/1024).toFixed(2)} MB | ${file.modified_str}
                    </small>
                </div>
                <div class="col-md-3">
                    <span class="badge bg-warning status-badge">修复完成</span>
                </div>
                <div class="col-md-3 text-end">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="previewFile('outputs', '${file.name}')">
                            <i class="bi bi-eye"></i> 预览
                        </button>
                        <button class="btn btn-outline-success" onclick="downloadFile('outputs', '${file.name}')">
                            <i class="bi bi-download"></i> 下载
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteFile('outputs', '${file.name}')">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// 开始检查修复
function startValidateRepair() {
    const filename = document.getElementById('validateFileSelect').value;
    const isCheckOnly = document.getElementById('checkOnly').checked;
    
    if (!filename) {
        showMessage('错误', '请选择要检查的文件', 'error');
        return;
    }
    
    // 获取选中的检查项目
    const checkItems = [];
    document.querySelectorAll('.repair-options input[type="checkbox"]:checked').forEach(checkbox => {
        checkItems.push(checkbox.value);
    });
    
    if (checkItems.length === 0) {
        showMessage('错误', '请至少选择一个检查项目', 'error');
        return;
    }
    
    // 开始计时
    startTimer();
    
    const requestData = {
        filename: filename,
        check_items: checkItems,
        check_fix: !isCheckOnly
    };
    
    fetch('/api/validate_repair', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        stopTimer();
        
        if (data.success) {
            currentRepairResult = data;
            showRepairReport(data.report);
            showMessage('成功', isCheckOnly ? '检查完成' : data.message, 'success');
            refreshFiles();
        } else {
            showMessage('错误', data.error || '检查修复失败', 'error');
        }
    })
    .catch(error => {
        stopTimer();
        showMessage('错误', '检查修复失败: ' + error.message, 'error');
    });
}

// 显示修复报告
function showRepairReport(report) {
    const reportSection = document.getElementById('repairReportSection');
    const reportContent = document.getElementById('repairReportContent');
    
    if (!report) {
        reportSection.style.display = 'none';
        return;
    }
    
    let reportHtml = '<div class="row">';
    
    // 显示总结信息
    if (report.summary) {
        reportHtml += `
            <div class="col-md-12 mb-3">
                <h6>检查总结</h6>
                <div class="alert alert-info">
                    <strong>总问题数:</strong> ${report.summary.total_issues || 0}<br>
                    <strong>处理时间:</strong> ${report.summary.processing_time || '未知'}
                </div>
            </div>
        `;
    }
    
    // 显示各类问题统计
    const issueTypes = ['movement_issues', 'translation_issues', 'llm_issues', 'duplicate_issues', 'long_issues','func_duplicate_issues'];
    const issueNames = {
        'func_duplicate_issues': '功能/事件重复问题',
        'movement_issues': '数据移动类型问题',
        'translation_issues': '英文翻译问题',
        'llm_issues': 'LLM检查问题',
        'duplicate_issues': '重复数据问题',
        'long_issues': '功能过程超长问题'
    };
    
    issueTypes.forEach(issueType => {
        if (report[issueType] && report[issueType].length > 0) {
            reportHtml += `
                <div class="col-md-6 mb-3">
                    <h6>${issueNames[issueType]}</h6>
                    <div class="alert alert-warning">
                        <strong>问题数量:</strong> ${report[issueType].length}
                    </div>
                </div>
            `;
        }
    });
    
    reportHtml += '</div>';
    
    reportContent.innerHTML = reportHtml;
    reportSection.style.display = 'block';
}

// 查看详细报告
function viewDetailedReport() {
    if (!currentRepairResult) {
        showMessage('错误', '没有可查看的报告', 'error');
        return;
    }
    
    // 在新窗口中显示详细报告
    const reportWindow = window.open('', '_blank');
    reportWindow.document.write(`
        <html>
        <head>
            <title>详细修复报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .section { margin-bottom: 20px; }
                .issue { background: #f8f9fa; padding: 10px; margin: 5px 0; border-left: 4px solid #ffc107; }
                pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
            </style>
        </head>
        <body>
            <h1>COSMIC检查修复详细报告</h1>
            <pre>${JSON.stringify(currentRepairResult.report, null, 2)}</pre>
        </body>
        </html>
    `);
}

// 下载修复结果
function downloadRepairResult() {
    if (!currentRepairResult || !currentRepairResult.repair_filename) {
        showMessage('错误', '没有可下载的修复结果', 'error');
        return;
    }
    
    downloadFile('outputs', currentRepairResult.repair_filename);
}

// 开始计时
function startTimer() {
    const timerElement = document.getElementById('repairTimer');
    repairStartTime = Date.now();
    timerElement.style.display = 'inline-block';
    
    repairTimer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - repairStartTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        timerElement.innerHTML = `<i class="bi bi-clock me-1"></i>已用时: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

// 停止计时
function stopTimer() {
    if (repairTimer) {
        clearInterval(repairTimer);
        repairTimer = null;
    }
    document.getElementById('repairTimer').style.display = 'none';
}

// 预览文件
function previewFile(folder, filename) {
    window.open(`/api/preview/${folder}/${encodeURIComponent(filename)}`, '_blank');
}

// 下载文件
function downloadFile(folder, filename) {
    window.location.href = `/api/download/${folder}/${encodeURIComponent(filename)}`;
}

// 删除文件
function deleteFile(folder, filename) {
    if (!confirm(`确定要删除文件 "${filename}" 吗？`)) {
        return;
    }

    fetch(`/api/delete/${folder}/${encodeURIComponent(filename)}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('成功', '文件删除成功', 'success');
            refreshFiles();
        } else {
            showMessage('错误', data.error || '删除失败', 'error');
        }
    })
    .catch(error => {
        showMessage('错误', '删除失败: ' + error.message, 'error');
    });
}

// 显示消息
function showMessage(title, message, type = 'info') {
    const modal = document.getElementById('messageModal');
    const modalTitle = document.getElementById('messageModalTitle');
    const modalBody = document.getElementById('messageModalBody');
    
    modalTitle.textContent = title;
    modalBody.innerHTML = message.replace(/\n/g, '<br>');
    
    // 根据类型设置样式
    modalTitle.className = 'modal-title';
    if (type === 'error') {
        modalTitle.classList.add('text-danger');
    } else if (type === 'success') {
        modalTitle.classList.add('text-success');
    } else if (type === 'warning') {
        modalTitle.classList.add('text-warning');
    }
    
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}
