// COSMIC功能拆解系统前端JavaScript

// 全局变量
let currentSection = 'upload';
let timerInterval = null;
let startTime = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    // 初始化导航
    initializeNavigation();

    // 初始化文件上传
    initializeFileUpload();

    // 初始化提示词编辑器
    initializePromptEditor();

    // 恢复之前的section或显示默认区域
    const savedSection = sessionStorage.getItem('currentSection');
    if (savedSection) {
        sessionStorage.removeItem('currentSection');
        showSection(savedSection);
    } else {
        showSection('upload');
    }
}

// 初始化导航
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            if (section) {
                showSection(section);
                
                // 更新导航状态
                navLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            }
        });
    });
}

// 显示指定区域
function showSection(sectionName) {
    // 隐藏所有区域
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
        section.style.display = 'none';
    });

    // 显示指定区域
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.style.display = 'block';
        currentSection = sectionName;

        // 如果切换到配置页面，自动加载配置
        if (sectionName === 'config') {
            setTimeout(() => {
                loadConfig();
            }, 100);
        }
    }
}

// 初始化文件上传
function initializeFileUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    
    if (!uploadArea || !fileInput) return;
    
    // 点击上传区域（但不包括按钮）
    uploadArea.addEventListener('click', function(e) {
        // 如果点击的是按钮，不处理（避免重复触发）
        if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
            return;
        }
        fileInput.click();
    });
    
    // 文件选择
    fileInput.addEventListener('change', function() {
        const files = this.files;
        if (files.length > 0) {
            uploadFile(files[0]);
        }
    });
    
    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            uploadFile(files[0]);
        }
    });
}

// 上传文件
function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    const progressContainer = document.getElementById('uploadProgress');
    const progressBar = progressContainer.querySelector('.progress-bar');
    
    // 显示进度条
    progressContainer.style.display = 'block';
    progressBar.style.width = '0%';
    
    fetch('/api/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        progressContainer.style.display = 'none';
        
        if (data.success) {
            showMessage('成功', `文件上传成功: ${data.filename}`, 'success');
            
            // 显示验证结果
            if (data.validation && !data.validation.valid) {
                showMessage('格式警告', `文件格式验证失败: ${data.validation.message}`, 'warning');
            }
            
            // 刷新文件列表
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showMessage('错误', data.error, 'danger');
        }
    })
    .catch(error => {
        progressContainer.style.display = 'none';
        showMessage('错误', '上传失败: ' + error.message, 'danger');
    });
}

// 预览文件
function previewFile(folder, filename) {
    const url = `/api/preview/${folder}/${filename}`;
    window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
}

// 下载文件
function downloadFile(folder, filename) {
    const url = `/download/${folder}/${filename}`;
    window.open(url, '_blank');
}

// 删除文件
function deleteFile(folder, filename) {
    if (!confirm(`确定要删除文件 "${filename}" 吗？`)) {
        return;
    }
    
    let apiUrl;
    if (folder === 'uploads') {
        apiUrl = `/api/delete_upload/${filename}`;
    } else if (folder === 'outputs') {
        apiUrl = `/api/delete_output/${filename}`;
    } else if (folder === 'words') {
        apiUrl = `/api/delete_word/${filename}`;
    } else {
        showMessage('错误', '无效的文件夹', 'danger');
        return;
    }
    
    fetch(apiUrl, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('成功', data.message, 'success');
            
            // 移除文件项
            const fileItem = document.querySelector(`[data-filename="${filename}"]`);
            if (fileItem) {
                fileItem.remove();
            }
        } else {
            showMessage('错误', data.error, 'danger');
        }
    })
    .catch(error => {
        showMessage('错误', '删除失败: ' + error.message, 'danger');
    });
}

// 开始功能拆解
function startDecompose() {
    const fileSelect = document.getElementById('decomposeFileSelect');
    const promptSelect = document.getElementById('promptFileSelect');

    const filename = fileSelect.value;
    const promptFile = promptSelect.value;

    if (!filename) {
        showMessage('错误', '请选择要拆解的文件', 'warning');
        return;
    }

    if (!promptFile) {
        showMessage('错误', '请选择提示词文件', 'warning');
        return;
    }

    // 显示加载状态和启动计时器
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>拆解中...';
    btn.disabled = true;

    startTimer('decomposeTimer');

    fetch('/api/cosmic_decompose', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            filename: filename,
            prompt_file: promptFile
        })
    })
    .then(response => response.json())
    .then(data => {
        stopTimer('decomposeTimer');
        btn.innerHTML = originalText;
        btn.disabled = false;

        if (data.success) {
            showMessage('成功', `功能拆解完成: ${data.output_filename}`, 'success');
            // 不跳转页面，保持在当前页面
            refreshFileList();
        } else {
            showMessage('错误', data.error, 'danger');
        }
    })
    .catch(error => {
        stopTimer('decomposeTimer');
        btn.innerHTML = originalText;
        btn.disabled = false;
        showMessage('错误', '拆解失败: ' + error.message, 'danger');
    });
}

// 开始检查修复
function startValidateRepair() {
    const fileSelect = document.getElementById('validateFileSelect');
    const filename = fileSelect.value;

    if (!filename) {
        showMessage('错误', '请选择要检查的文件', 'warning');
        return;
    }

    // 显示加载状态和启动计时器
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>检查修复中...';
    btn.disabled = true;

    startTimer('repairTimer');

    fetch('/api/validate_repair', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            filename: filename
        })
    })
    .then(response => response.json())
    .then(data => {
        stopTimer('repairTimer');
        btn.innerHTML = originalText;
        btn.disabled = false;

        if (data.success) {
            showMessage('成功', `检查修复完成: ${data.repair_filename}`, 'success');

            // 显示检查报告
            if (data.report) {
                showRepairReport(data.report);
            }

            // 不跳转页面，保持在当前页面
            refreshFileList();
        } else {
            showMessage('错误', data.error, 'danger');
        }
    })
    .catch(error => {
        stopTimer('repairTimer');
        btn.innerHTML = originalText;
        btn.disabled = false;
        showMessage('错误', '检查修复失败: ' + error.message, 'danger');
    });
}

// 开始生成文档
function startGenerateDocument() {
    const fileSelect = document.getElementById('documentFileSelect');
    const filename = fileSelect.value;

    if (!filename) {
        showMessage('错误', '请选择要生成文档的文件', 'warning');
        return;
    }

    // 显示加载状态和启动计时器
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>生成中...';
    btn.disabled = true;

    startTimer('documentTimer');

    fetch('/api/generate_document', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            filename: filename
        })
    })
    .then(response => response.json())
    .then(data => {
        stopTimer('documentTimer');
        btn.innerHTML = originalText;
        btn.disabled = false;

        if (data.success) {
            showMessage('成功', `${data.message} : ${data.md_filename}`, 'success');
            // 不跳转页面，保持在当前页面
            refreshFileList();
        } else {
            showMessage('错误', data.error, 'danger');
        }
    })
    .catch(error => {
        stopTimer('documentTimer');
        btn.innerHTML = originalText;
        btn.disabled = false;
        showMessage('错误', '文档生成失败: ' + error.message, 'danger');
    });
}

// 转换为Word文档
function convertToWord(filename) {
    if (!confirm(`确定要将 "${filename}" 转换为Word文档吗？`)) {
        return;
    }

    fetch('/api/convert_to_word', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            filename: filename
        })
    })
    .then(response => {
        if (response.ok) {
            // 直接下载文件
            return response.blob();
        } else {
            return response.json().then(data => {
                throw new Error(data.error || '转换失败');
            });
        }
    })
    .then(blob => {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename.replace('.md', '.docx');
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        showMessage('成功', 'Word文档转换完成', 'success');
    })
    .catch(error => {
        showMessage('错误', '转换失败: ' + error.message, 'danger');
    });
}

// 刷新文件列表
function refreshFiles() {
    location.reload();
}

// 加载提示词内容
function loadPromptContent() {
    const select = document.getElementById('promptEditSelect');
    const textarea = document.getElementById('promptContent');
    const saveAsBtn = document.getElementById('saveAsBtn');
    const filename = select.value;

    if (!filename) {
        textarea.value = '';
        textarea.placeholder = '请选择提示词文件后编辑内容...';
        if (saveAsBtn) saveAsBtn.style.display = 'inline-block';
        return;
    }

    // 检查是否为系统提示词
    const selectedOption = select.options[select.selectedIndex];
    const isSystem = selectedOption && selectedOption.getAttribute('data-is-system') === 'true';

    // 控制另存按钮的显示
    if (saveAsBtn) {
        saveAsBtn.style.display = isSystem ? 'none' : 'inline-block';
    }

    fetch(`/api/prompt/${filename}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            textarea.value = data.content;
            textarea.placeholder = '';
        } else {
            showMessage('错误', data.error, 'danger');
            textarea.value = '';
        }
    })
    .catch(error => {
        showMessage('错误', '加载失败: ' + error.message, 'danger');
        textarea.value = '';
    });
}

// 保存提示词内容
function savePromptContent() {
    const select = document.getElementById('promptEditSelect');
    const textarea = document.getElementById('promptContent');
    const filename = select.value;
    const content = textarea.value;

    if (!filename) {
        showMessage('错误', '请先选择提示词文件', 'warning');
        return;
    }

    if (!content.trim()) {
        showMessage('错误', '内容不能为空', 'warning');
        return;
    }

    fetch(`/api/prompt/${filename}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            content: content
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('成功', data.message, 'success');
        } else {
            showMessage('错误', data.error, 'danger');
        }
    })
    .catch(error => {
        showMessage('错误', '保存失败: ' + error.message, 'danger');
    });
}

// 另存提示词内容
function savePromptAs() {
    const textarea = document.getElementById('promptContent');
    const content = textarea.value;

    if (!content.trim()) {
        showMessage('错误', '内容不能为空', 'danger');
        return;
    }

    // 弹出对话框让用户输入新文件名
    const newFilename = prompt('请输入新文件名（不需要输入.md扩展名）：', '');
    if (!newFilename || !newFilename.trim()) {
        return;
    }

    fetch('/api/prompt/save-as', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            content: content,
            filename: newFilename.trim()
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('成功', data.message, 'success');
            // 刷新页面以更新提示词文件列表
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showMessage('错误', data.error, 'danger');
        }
    })
    .catch(error => {
        showMessage('错误', '另存失败: ' + error.message, 'danger');
    });
}

// 重置提示词内容
function resetPromptContent() {
    if (!confirm('确定要重置内容吗？未保存的修改将丢失。')) {
        return;
    }

    loadPromptContent();
}

// 计时器相关函数
function startTimer(elementId) {
    startTime = Date.now();
    const element = document.getElementById(elementId);
    if (!element) return;

    element.style.display = 'block';

    timerInterval = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        element.textContent = `已用时: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

function stopTimer(elementId) {
    if (timerInterval) {
        clearInterval(timerInterval);
        timerInterval = null;
    }

    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'none';
    }
}

// 刷新文件列表
function refreshFileList() {
    // 保存当前section
    const currentSectionValue = currentSection;

    // 刷新页面但保持当前section
    setTimeout(() => {
        // 将当前section保存到sessionStorage
        sessionStorage.setItem('currentSection', currentSectionValue);
        location.reload();
    }, 1000);
}

// 显示修复报告
function showRepairReport(report) {
    if (!report) return;

    let reportHtml = '<div class="alert alert-success mt-3">';
    reportHtml += '<h6><i class="bi bi-check-circle me-2"></i>修复报告</h6>';

    if (report.summary) {
        const summary = report.summary;
        reportHtml += '<div class="row mb-3">';
        reportHtml += '<div class="col-md-3"><div class="text-center"><h5 class="text-primary">' + (report.original_records || 0) + '</h5><small>原始记录</small></div></div>';
        reportHtml += '<div class="col-md-3"><div class="text-center"><h5 class="text-success">' + (report.fixed_records || 0) + '</h5><small>修复后记录</small></div></div>';
        reportHtml += '<div class="col-md-3"><div class="text-center"><h5 class="text-warning">' + (summary.total_issues || 0) + '</h5><small>发现问题</small></div></div>';
        reportHtml += '<div class="col-md-3"><div class="text-center"><h5 class="text-info">' + Math.round(report.duration_seconds || 0) + 's</h5><small>处理时间</small></div></div>';
        reportHtml += '</div>';

        if (summary.total_issues > 0) {
            reportHtml += '<div class="mb-3">';
            reportHtml += '<h6>问题分类统计:</h6>';
            reportHtml += '<ul class="list-unstyled">';
            if (summary.python_issues_found > 0) {
                reportHtml += `<li><i class="bi bi-bug text-danger me-2"></i>Python检查问题: ${summary.python_issues_found}个</li>`;
            }
            if (summary.llm_issues_found > 0) {
                reportHtml += `<li><i class="bi bi-robot text-warning me-2"></i>LLM修复问题: ${summary.llm_issues_found}个</li>`;
            }
            if (summary.consistency_issues_found > 0) {
                reportHtml += `<li><i class="bi bi-arrow-repeat text-info me-2"></i>一致性问题: ${summary.consistency_issues_found}个</li>`;
            }
            if (summary.translation_issues_found > 0) {
                reportHtml += `<li><i class="bi bi-translate text-secondary me-2"></i>翻译问题: ${summary.translation_issues_found}个</li>`;
            }
            reportHtml += '</ul>';
            reportHtml += '</div>';
        }

        if (summary.data_changed) {
            reportHtml += '<div class="alert alert-warning alert-sm mb-2"><i class="bi bi-exclamation-triangle me-2"></i>数据已被修改</div>';
        } else {
            reportHtml += '<div class="alert alert-success alert-sm mb-2"><i class="bi bi-check-circle me-2"></i>数据无需修改</div>';
        }
    }

    reportHtml += `<small class="text-muted">处理时间: ${report.processing_time || '未知'}</small>`;
    reportHtml += '</div>';

    // 在检查修复区域显示报告
    const repairSection = document.querySelector('#validate-section .card-body');
    if (repairSection) {
        // 移除之前的报告
        const existingReport = repairSection.querySelector('.alert-success');
        if (existingReport) {
            existingReport.remove();
        }
        repairSection.insertAdjacentHTML('beforeend', reportHtml);
    }
}

// 初始化提示词编辑器
function initializePromptEditor() {
    const editMode = document.getElementById('editMode');
    const previewMode = document.getElementById('previewMode');
    const editArea = document.getElementById('promptEditArea');
    const previewArea = document.getElementById('promptPreviewArea');
    const textarea = document.getElementById('promptContent');
    const previewContent = document.getElementById('promptPreviewContent');

    if (!editMode || !previewMode || !editArea || !previewArea || !textarea || !previewContent) {
        return;
    }

    // 切换到编辑模式
    editMode.addEventListener('change', function() {
        if (this.checked) {
            editArea.style.display = 'block';
            previewArea.style.display = 'none';
        }
    });

    // 切换到预览模式
    previewMode.addEventListener('change', function() {
        if (this.checked) {
            editArea.style.display = 'none';
            previewArea.style.display = 'block';
            updatePreview();
        }
    });

    // 更新预览内容
    function updatePreview() {
        const content = textarea.value;
        if (!content.trim()) {
            previewContent.innerHTML = '<p class="text-muted">请先编辑内容，然后切换到预览模式查看效果</p>';
            return;
        }

        // 检查是否包含Mermaid图形
        if (content.toLowerCase().includes('mermaid') || content.includes('```mermaid')) {
            // 包含Mermaid，使用完整的预览
            showMermaidPreview(content);
        } else {
            // 简单的Markdown渲染（基础功能）
            let html = content
                // 标题
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                // 粗体
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                // 斜体
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                // 代码块
                .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
                // 行内代码
                .replace(/`(.*?)`/g, '<code>$1</code>')
                // 列表
                .replace(/^\* (.*$)/gim, '<li>$1</li>')
                .replace(/^- (.*$)/gim, '<li>$1</li>')
                // 换行
                .replace(/\n/g, '<br>');

            // 包装列表项
            html = html.replace(/(<li>.*<\/li>)/g, '<ul>$1</ul>');

            previewContent.innerHTML = html;
        }
    }

    // 显示包含Mermaid的预览
    function showMermaidPreview(content) {
        // 创建一个新窗口来显示完整的Mermaid预览
        const previewWindow = window.open('', '_blank', 'width=1000,height=800,scrollbars=yes');

        // 发送内容到后端进行渲染
        fetch('/api/preview_markdown', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                content: content
            })
        })
        .then(response => response.text())
        .then(html => {
            previewWindow.document.write(html);
            previewWindow.document.close();
        })
        .catch(error => {
            previewWindow.document.write(`
                <html>
                <head><title>预览错误</title></head>
                <body>
                    <h1>预览失败</h1>
                    <p>无法加载Mermaid预览: ${error.message}</p>
                </body>
                </html>
            `);
            previewWindow.document.close();
        });

        // 在当前预览区域显示提示
        previewContent.innerHTML = '<div class="alert alert-info"><i class="bi bi-info-circle me-2"></i>Mermaid图形预览已在新窗口中打开</div>';
    }
}

// Provider配置相关函数
function updateProviderConfig() {
    const provider = document.getElementById('config_LLM_PROVIDER').value;
    const configs = document.querySelectorAll('.provider-config');

    // 隐藏所有配置
    configs.forEach(config => {
        config.style.display = 'none';
    });

    // 显示选中的配置
    const selectedConfig = document.getElementById(`${provider}-config`);
    if (selectedConfig) {
        selectedConfig.style.display = 'block';
    }
}

// 切换密码显示/隐藏
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'bi bi-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'bi bi-eye';
    }
}

// 加载配置
function loadConfig() {
    const container = document.getElementById('configContainer');
    if (!container) return;

    // 显示加载状态
    container.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载配置...</p>
        </div>
    `;

    fetch('/api/config')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderConfigForm(data.config_groups);
        } else {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    加载配置失败: ${data.error}
                </div>
            `;
        }
    })
    .catch(error => {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                加载配置失败: ${error.message}
            </div>
        `;
    });
}

function renderConfigForm(configGroups) {
    const container = document.getElementById('configContainer');
    if (!container) return;

    let html = '';
    
    for (const [groupName, groupConfigs] of Object.entries(configGroups)) {
        html += `
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">${groupName}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
        `;
        
        for (const [key, config] of Object.entries(groupConfigs)) {
            const inputId = `config_${key}`;
            const value = config.value;
            const type = config.type;
            const description = config.description;
            const min = config.min;
            const max = config.max;
            const step = config.step;
            const options = config.options;
            
            let inputHtml = '';
            
            if (type === 'boolean') {
                inputHtml = `
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="${inputId}" ${value ? 'checked' : ''}>
                        <label class="form-check-label" for="${inputId}">启用</label>
                    </div>
                `;
            } else if (type === 'select') {
                inputHtml = `<select class="form-select" id="${inputId}">`;
                for (const option of options) {
                    inputHtml += `<option value="${option}" ${value === option ? 'selected' : ''}>${option}</option>`;
                }
                inputHtml += '</select>';
            } else if (type === 'textarea') {
                inputHtml = `<textarea class="form-control" id="${inputId}" rows="3">${value}</textarea>`;
            } else if (type === 'password') {
                inputHtml = `
                    <div class="input-group">
                        <input type="password" class="form-control" id="${inputId}" value="${value}">
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('${inputId}')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                `;
            } else if (type === 'number') {
                let attrs = `type="number" value="${value}"`;
                if (min !== undefined) attrs += ` min="${min}"`;
                if (max !== undefined) attrs += ` max="${max}"`;
                if (step !== undefined) attrs += ` step="${step}"`;
                inputHtml = `<input class="form-control" id="${inputId}" ${attrs}>`;
            } else {
                inputHtml = `<input class="form-control" id="${inputId}" type="${type}" value="${value}">`;
            }
            
            html += `
                <div class="col-md-6 mb-3">
                    <label class="form-label">${key}</label>
                    ${inputHtml}
                    <small class="form-text text-muted">${description}</small>
                </div>
            `;
        }
        
        html += `
                    </div>
                </div>
            </div>
        `;
    }
    
    container.innerHTML = html;
}

// 保存配置
function saveConfig() {
    if (!confirm('确定要保存配置吗？修改后需要重启系统才能生效。')) {
        return;
    }

    const configData = {};

    // 收集所有配置项
    const configInputs = document.querySelectorAll('[id^="config_"]');
    configInputs.forEach(input => {
        const key = input.id.replace('config_', '');
        let value;
        
        if (input.type === 'checkbox') {
            value = input.checked;
        } else {
            value = input.value.trim();
        }
        
        if (value !== '' && value !== null && value !== undefined) {
            configData[key] = value;
        }
    });

    fetch('/api/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            configs: configData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('成功', data.message, 'success');
        } else {
            let errorMsg = data.error;
            if (data.validation_errors) {
                errorMsg += '\n\n验证错误:\n' + Object.entries(data.validation_errors)
                    .map(([key, error]) => `${key}: ${error}`)
                    .join('\n');
            }
            showMessage('错误', errorMsg, 'danger');
        }
    })
    .catch(error => {
        showMessage('错误', '保存配置失败: ' + error.message, 'danger');
    });
}

// 导出配置
function exportConfig() {
    fetch('/api/config/export')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 创建下载链接
            const blob = new Blob([data.content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'cosmic_config.env';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            showMessage('成功', '配置导出成功', 'success');
        } else {
            showMessage('错误', data.error, 'danger');
        }
    })
    .catch(error => {
        showMessage('错误', '导出配置失败: ' + error.message, 'danger');
    });
}

// 导入配置
function importConfig() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.env,text/plain';
    
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            const content = e.target.result;
            
            if (!confirm('确定要导入配置吗？这将覆盖当前配置。')) {
                return;
            }
            
            fetch('/api/config/import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content: content
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('成功', data.message, 'success');
                    // 重新加载配置
                    loadConfig();
                } else {
                    showMessage('错误', data.error, 'danger');
                }
            })
            .catch(error => {
                showMessage('错误', '导入配置失败: ' + error.message, 'danger');
            });
        };
        reader.readAsText(file);
    };
    
    input.click();
}

// 页面加载时自动加载配置
document.addEventListener('DOMContentLoaded', function() {
    // 延迟加载配置，避免与其他初始化冲突
    setTimeout(() => {
        if (currentSection === 'config') {
            loadConfig();
        }
    }, 1000);
});

// 显示消息
function showMessage(title, message, type = 'info') {
    const modal = document.getElementById('messageModal');
    const modalTitle = document.getElementById('messageModalTitle');
    const modalBody = document.getElementById('messageModalBody');
    
    modalTitle.textContent = title;
    modalBody.innerHTML = `<div class="alert alert-${type} mb-0">${message}</div>`;
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// 工具函数：格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函数：格式化日期
function formatDate(timestamp) {
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('zh-CN');
}
