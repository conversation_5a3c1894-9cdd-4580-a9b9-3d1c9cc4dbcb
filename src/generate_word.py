import os
import re
import time
import shutil
from playwright.sync_api import sync_playwright
from tqdm import tqdm

HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8" />
    <title>Mermaid 图表</title>
    <script src="{mermaid_js_path}"></script>
</head>
<body>
    <div id="chart" class="mermaid">
        {input_str}
    </div>
</body>
</html>
"""

def generate_mermaid_jpg(input_str, output_path, temp_html_dir):
    mermaid_js_path = os.path.join(os.path.dirname(__file__), "mermaid.min.js")
    #mermaid_js_path =  "mermaid.min.js"
    html_content = HTML_TEMPLATE.format(input_str=input_str, mermaid_js_path=mermaid_js_path)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    html_file = f"{temp_html_dir}/{time.time()}.html"
    with open(html_file, "w", encoding="utf-8") as f:
        f.write(html_content)
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto(f"file://{os.path.abspath(html_file)}")
            page.wait_for_selector(".mermaid")
            page.wait_for_timeout(1000)
            chart_element = page.locator("svg") # 原来是.mermaid，发现会有问题（右侧有大片空白），所以改成svg
            chart_element.screenshot(path=output_path)
            browser.close()
    except Exception as e:
        print(html_file, str(e))
    finally:
         os.remove(html_file)


def replace_mermaid_with_images(input_md_file):
    """将markdown文件中的mermaid代码块替换为图片引用"""
    output_dir = os.path.dirname(input_md_file)
    file_name = os.path.basename(input_md_file)

    with open(input_md_file, 'r', encoding='utf-8') as file:
        with open(input_md_file.replace('.md', '_pre_process.md'), 'w', encoding='utf-8') as file_pre_process:
            lines = file.readlines()
            for line in lines:
                if line.startswith('<div class="mermaid">'):
                    line = "```mermaid\n"
                line = line.replace('</div>', '```')
                line = line.replace('<br/>', '  ')
                line = line.replace('***', '**')
                file_pre_process.write(line)

    with open(input_md_file.replace('.md', '_pre_process.md'), 'r', encoding='utf-8') as file:
        md_content = file.read()

    # 提取所有mermaid代码块
    mermaid_blocks = re.findall(r'```mermaid\n(.*?)\n```', md_content, re.DOTALL)
    #print(len(mermaid_blocks))
    for i, mermaid_code in tqdm(enumerate(mermaid_blocks), total=len(mermaid_blocks), desc="根据mermaid代码生成图片中..."):
        img_filename = f"mermaid_{i+1}.png"
        img_path = f"{output_dir}/images-{file_name}/{img_filename}"
        generate_mermaid_jpg(mermaid_code, img_path, output_dir)
        mermaid_block = f"```mermaid\n{mermaid_code}\n```"
        img_reference = f"![mermaid_{i+1}](images-{file_name}/{img_filename})"
        md_content = md_content.replace(mermaid_block, img_reference)
    
    # 去掉原来的序号
    processed_content = remove_title_numbers(md_content)
    # 保存替换后的markdown文件
    output_md_path = input_md_file.replace('.md', '_with_images.md')
    with open(output_md_path, 'w', encoding='utf-8') as file:
        file.write(processed_content)



def remove_title_numbers(text):
    """
    去掉Markdown标题后的数字
    
    Args:
        text (str): 输入的文本内容
        
    Returns:
        str: 处理后的文本内容
    """
    # 匹配Markdown标题的正则表达式
    # 匹配 # 到 ###### 开头的行，后面跟着数字和点，然后是标题内容
    pattern = r'^(#{1,6})\s+(\d+\.)*\d+\s*(.+)$'
    
    lines = text.split('\n')
    processed_lines = []
    
    for line in lines:
        match = re.match(pattern, line)
        if match:
            # 提取标题级别和标题内容
            title_level = match.group(1)
            title_content = match.group(3)
            # 重新组合标题，去掉数字部分
            new_line = f"{title_level} {title_content}"
            processed_lines.append(new_line)
        else:
            # 不是标题行，保持原样
            processed_lines.append(line)
    
    return '\n'.join(processed_lines)


def md2docx(input_md_file, reference_doc="src/cosmic-reference.docx") -> str:
    word_file = os.path.basename(input_md_file).replace('.md', '.docx')
    replace_mermaid_with_images(input_md_file)
    image_md_file = input_md_file.replace('.md', '_with_images.md')
    output_dir = os.path.dirname(input_md_file)
    image_md_file_name = os.path.basename(image_md_file)
    reference_doc_path = os.path.abspath(reference_doc)
    cmd = f"cd {output_dir} && pandoc {image_md_file_name} -o {word_file} --reference-doc={reference_doc_path}"
    cmd_result = os.system(cmd)
    if cmd_result:
        print(f"pandoc 转换失败: {cmd} \n\n {cmd_result}")
    else:
        print(f"pandoc 转换完成: {output_dir}/{word_file}")
    # 删除中间文件
    os.remove(input_md_file.replace('.md', '_pre_process.md'))
    os.remove(input_md_file.replace('.md', '_with_images.md'))
    # 删除临时图片目录
    img_path = f"{output_dir}/images-{os.path.basename(input_md_file)}"
    shutil.rmtree(img_path)
    return input_md_file.replace('.md', '.docx')


if __name__ == "__main__":
    import sys    
    if len(sys.argv) > 1:
        input_md_file = sys.argv[1] # COSMIC数据文件
    else:
        #input_md_file = "data/附件4：甘肃移动-软件功能清单-0731.xlsx"
        print("Usage: python main.py <input_md_file>")
        sys.exit(1)
    #reference_doc = "cosmic-reference.docx" # word样式文件
    #replace_mermaid_with_images(input_md_file)
    md2docx(input_md_file)


# uv pip install playwright
# playwright install chromium
# playwright install-deps

# 下载mermaid.min.js，和当前文件放在一个文件夹下
# 目录中不能有特殊字符

