#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC功能拆解系统 Web UI

提供Web界面用于：
1. 软件功能清单文件上传管理
2. COSMIC功能拆解
3. 拆解结果检查和修复
4. Word文档转换
5. 配置管理
"""

import os
import glob
import json
import time
import shutil
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from flask_cors import CORS
from werkzeug.utils import secure_filename
import pandas as pd
import markdown
from pygments import highlight
from pygments.lexers import get_lexer_by_name
from pygments.formatters import html
import llm_util

# 导入项目模块
from main import do_cosmic_file
from validator import CosmicValidator
from doc_generator import RequirementGenerator
from generate_word import md2docx
import config

app = Flask(__name__, template_folder='../templates', static_folder='../static')
CORS(app)

# 配置
UPLOAD_FOLDER = 'data/uploads'
OUTPUT_FOLDER = 'data/outputs'
WORDS_FOLDER = 'data/words'
PROMPTS_FOLDER = 'data/prompts'
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
ENV_FILE = '.env'

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['OUTPUT_FOLDER'] = OUTPUT_FOLDER
app.config['WORDS_FOLDER'] = WORDS_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# 确保目录存在
for folder in [UPLOAD_FOLDER, OUTPUT_FOLDER, WORDS_FOLDER, PROMPTS_FOLDER]:
    os.makedirs(folder, exist_ok=True)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS and  not filename.startswith('~')

def clean_filename(filename):
    """清理文件名，移除或替换Linux文件系统的非法字符"""
    import re

    # 移除或替换Linux文件系统的非法字符
    # Linux不允许: / \ 0 等字符，包括中英文冒号
    illegal_chars = r'[<>:"：/\\|?*\x00-\x1f]'

    # 替换非法字符为下划线
    cleaned_filename = re.sub(illegal_chars, '_', filename)

    # 移除开头和结尾的下划线
    cleaned_filename = cleaned_filename.strip('_')

    # 将连续的下划线替换为单个下划线
    cleaned_filename = re.sub(r'_+', '_', cleaned_filename)

    # 确保文件名不为空
    if not cleaned_filename:
        cleaned_filename = "uploaded_file"

    # 确保文件名不超过255个字符（文件系统限制）
    if len(cleaned_filename) > 255:
        name, ext = os.path.splitext(cleaned_filename)
        max_name_length = 255 - len(ext)
        cleaned_filename = name[:max_name_length] + ext

    return cleaned_filename

def validate_excel_format(file_path):
    """验证Excel文件格式并识别文件类型"""
    try:
        # 读取第2个sheet（索引为1）
        df = llm_util.parse_excel_file(file_path)

        # 定义两种文件类型的必需列
        functional_list_columns = [
            '一级功能模块', '二级功能模块', '三级功能模块',
            '功能过程', '功能描述', '预估工作量（人天）'
        ]

        functional_decompose_columns = [
            '一级功能模块', '二级功能模块', '三级功能模块',
            '功能用户', '触发事件', '功能过程', '子过程描述',
            '数据移动类型', '数据组', '数据属性', 'CFP'
        ]

        # 检查功能清单文件格式
        missing_list_columns = []
        for col in functional_list_columns:
            if col not in df.columns:
                missing_list_columns.append(col)

        if not missing_list_columns:
            return True, "功能清单文件格式验证通过", "functional_list"

        # 检查功能拆解文件格式
        missing_decompose_columns = []
        for col in functional_decompose_columns:
            if col not in df.columns:
                missing_decompose_columns.append(col)

        if not missing_decompose_columns:
            return True, "功能拆解文件格式验证通过", "functional_decompose"

        # 如果都不匹配，返回错误信息
        return False, f"文件格式不匹配。功能清单文件需要列: {', '.join(functional_list_columns)}；功能拆解文件需要列: {', '.join(functional_decompose_columns)}", None

    except Exception as e:
        return False, f"文件读取错误: {str(e)}", None

def get_uploaded_files():
    """获取已上传的文件列表"""
    files = []
    if os.path.exists(UPLOAD_FOLDER):
        for filename in os.listdir(UPLOAD_FOLDER):
            if allowed_file(filename):
                file_path = os.path.join(UPLOAD_FOLDER, filename)
                file_info = {
                    'name': filename,
                    'path': file_path,
                    'size': os.path.getsize(file_path),
                    'modified': os.path.getmtime(file_path),
                    'modified_str': datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S'),
                    'type': 'functional_list',
                    'folder': 'uploads'
                }
                files.append(file_info)

    # 按修改时间排序
    files.sort(key=lambda x: x['modified'], reverse=True)
    return files

def get_output_files():
    """获取输出文件列表（功能拆解文件）"""
    files = []
    if os.path.exists(OUTPUT_FOLDER):
        for filename in os.listdir(OUTPUT_FOLDER):
            # 过滤~开头的临时文件
            if filename.endswith(('.xlsx', '.csv')) and not filename.startswith('~'):
                file_path = os.path.join(OUTPUT_FOLDER, filename)
                file_info = {
                    'name': filename,
                    'path': file_path,
                    'size': os.path.getsize(file_path),
                    'modified': os.path.getmtime(file_path),
                    'modified_str': datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S'),
                    'type': 'functional_decompose',
                    'folder': 'outputs',
                    'display_name': '功能拆解文件'
                }
                files.append(file_info)

    files.sort(key=lambda x: x['modified'], reverse=True)
    return files

def get_word_files():
    """获取Word文档文件列表"""
    files = []
    if os.path.exists(WORDS_FOLDER):
        for filename in os.listdir(WORDS_FOLDER):
            if filename.endswith(('.md', '.docx')):
                file_path = os.path.join(WORDS_FOLDER, filename)
                file_info = {
                    'name': filename,
                    'path': file_path,
                    'size': os.path.getsize(file_path),
                    'modified': os.path.getmtime(file_path),
                    'modified_str': datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')
                }
                files.append(file_info)

    files.sort(key=lambda x: x['modified'], reverse=True)
    return files

def get_prompt_files():
    """获取提示词文件列表"""
    files = []
    prompt_dir = 'prompt'
    if os.path.exists(prompt_dir):
        for filename in os.listdir(prompt_dir):
            # 显示以'prompt'开头的.md文件和系统修复提示词
            if filename.endswith('.md') and (filename.lower().startswith('prompt')):
                # or filename == 'check_fix_prompt.md'):
                file_path = os.path.join(prompt_dir, filename)

                # 特殊处理系统修复提示词
                # if filename == 'check_fix_prompt.md':
                #     display_name = '系统修复提示词'
                #     is_system = False
                # else:
                display_name = filename.replace('.md', '').replace('prompt_', '').replace('prompt', '功能拆解提示词')
                is_system = True

                file_info = {
                    'name': filename,
                    'path': file_path,
                    'display_name': display_name,
                    'is_system': is_system
                }
                files.append(file_info)

    # 按文件名排序，系统文件排在前面
    files.sort(key=lambda x: (not x.get('is_system', False), x['name']))
    return files
def generate_flowchart_html(sequence_content):
    """从时序图内容生成流程图HTML"""
    import re

    # 解析时序图内容，提取参与者和交互
    participants = []
    interactions = []
    participant_map = {}

    lines = sequence_content.strip().split('\n')
    for line in lines:
        line = line.strip()
        if line.startswith('participant '):
            # 提取参与者
            match = re.match(r'participant\s+(\w+)\s+as\s+(.+)', line)
            if match:
                participant_id = match.group(1)
                participant_name = match.group(2)
                participants.append({'id': participant_id, 'name': participant_name})
                participant_map[participant_id] = participant_name
        elif any(arrow in line for arrow in ['->', '-->>', '-->']):
            # 提取交互
            arrow_type = 'solid'
            if '->>' in line:
                parts = line.split('->>')
                arrow_type = 'solid'
            elif '-->' in line:
                parts = line.split('-->')
                arrow_type = 'dashed'
            elif '->' in line:
                parts = line.split('->')
                arrow_type = 'solid'
            else:
                continue

            if len(parts) == 2:
                from_actor = parts[0].strip()
                to_and_msg = parts[1].strip()
                if ':' in to_and_msg:
                    to_actor, message = to_and_msg.split(':', 1)
                    to_actor = to_actor.strip()
                    message = message.strip()
                else:
                    to_actor = to_and_msg
                    message = ''

                # 查找参与者在列表中的位置
                from_index = -1
                to_index = -1
                for i, p in enumerate(participants):
                    if p['id'] == from_actor:
                        from_index = i
                    if p['id'] == to_actor:
                        to_index = i

                if from_index >= 0 and to_index >= 0:
                    interactions.append({
                        'from': from_actor,
                        'to': to_actor,
                        'from_index': from_index,
                        'to_index': to_index,
                        'message': message,
                        'type': arrow_type
                    })

    if not participants:
        return '<div class="flowchart-container"><p>无法解析时序图内容</p></div>'

    # 生成流程图HTML
    flowchart_html = '<div class="flowchart-container">'

    # 添加参与者框
    for i, participant in enumerate(participants):
        flowchart_html += f'''
        <div class="participant-box" data-id="{participant['id']}" style="left: {i * 180 + 50}px;">
            {participant['name']}
        </div>
        '''

    # 添加交互箭头和消息
    for i, interaction in enumerate(interactions):
        arrow_class = 'solid-arrow' if interaction['type'] == 'solid' else 'dashed-arrow'

        # 计算箭头位置
        from_x = interaction['from_index'] * 180 + 110  # 参与者框中心
        to_x = interaction['to_index'] * 180 + 110
        y_pos = 120 + i * 60

        # 确定箭头方向
        if from_x < to_x:
            arrow_left = from_x
            arrow_width = to_x - from_x
            arrow_direction = 'right'
        else:
            arrow_left = to_x
            arrow_width = from_x - to_x
            arrow_direction = 'left'

        flowchart_html += f'''
        <div class="interaction" style="top: {y_pos}px; left: {arrow_left}px; width: {arrow_width}px;">
            <div class="arrow {arrow_class} arrow-{arrow_direction}"></div>
            <div class="message">{interaction['message']}</div>
        </div>
        '''

    flowchart_html += '</div>'
    return flowchart_html
def render_markdown_with_mermaid(markdown_content):
    """渲染Markdown内容，支持Mermaid图表和流程图备选方案"""
    # 使用markdown库渲染基本内容
    md = markdown.Markdown(extensions=['codehilite', 'fenced_code', 'tables', 'toc'])
    html_content = md.convert(markdown_content)

    # 查找并处理时序图，添加流程图备选方案
    import re

    def replace_mermaid_with_fallback(match):
        mermaid_content = match.group(1)
        if 'sequenceDiagram' in mermaid_content:
            # 生成流程图备选方案
            flowchart_html = generate_flowchart_html(mermaid_content)
            return f'''
            <div class="diagram-container">
                <div class="mermaid-diagram">
                    <pre class="mermaid">{mermaid_content}</pre>
                </div>
                <div class="fallback-flowchart" style="display: none;">
                    {flowchart_html}
                </div>
                <div class="diagram-toggle">
                    <button onclick="toggleDiagram(this)" class="toggle-btn">切换到流程图</button>
                </div>
            </div>
            '''
        else:
            return f'<pre class="mermaid">{mermaid_content}</pre>'

    # 替换mermaid代码块
    html_content = re.sub(r'<pre><code class="language-mermaid">(.*?)</code></pre>',
                         replace_mermaid_with_fallback, html_content, flags=re.DOTALL)

    # 读取HTML模板文件
    template_path = os.path.join('templates', 'document_template.html')
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            html_template = f.read()

        # 替换模板中的占位符
        html_template = html_template.replace('{{html_content}}', html_content)

    except FileNotFoundError:
        # 如果模板文件不存在，使用简单的HTML包装
        html_template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>功能需求文档</title>
            <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js"></script>
        </head>
        <body>
            <div class="container">
                {html_content}
            </div>
            <script>
                mermaid.initialize({{
                    startOnLoad: true,
                    theme: 'default',
                    securityLevel: 'loose'
                }});
            </script>
        </body>
        </html>
        """

    return html_template
def generate_excel_preview_html(file_path, df):
    """生成Excel预览HTML，支持合并单元格样式"""
    # 把NaN替换为空格
    df = df.fillna('')
    html_table = df.to_html(classes='table table-striped table-bordered table-sm',
                               table_id='preview-table',
                               escape=False)
    return f'<div class="table-responsive" style="max-height: 600px; overflow-y: auto;">{html_table}</div>'
    try:
        import openpyxl
        from openpyxl import load_workbook

        # 加载工作簿以获取合并单元格信息
        wb = load_workbook(file_path)
        ws = wb.active

        # 获取合并单元格范围
        merged_ranges = list(ws.merged_cells.ranges)

        # 生成HTML表格
        html = '<div class="table-responsive" style="max-height: 600px; overflow-y: auto;">'
        html += '<table class="table table-striped table-bordered table-sm" id="preview-table">'

        # 表头
        html += '<thead class="table-dark sticky-top">'
        html += '<tr>'
        for col in df.columns:
            html += f'<th>{col}</th>'
        html += '</tr>'
        html += '</thead>'

        # 表体
        html += '<tbody>'
        for idx, row in df.iterrows():
            html += '<tr>'
            for col_idx, (col, value) in enumerate(row.items()):
                # 检查是否在合并单元格中
                cell_coord = f"{openpyxl.utils.get_column_letter(col_idx + 1)}{idx + 2}"  # +2因为有表头
                is_merged = False
                colspan = 1
                rowspan = 1

                for merged_range in merged_ranges:
                    if cell_coord in merged_range:
                        is_merged = True
                        # 计算合并范围
                        min_col, min_row, max_col, max_row = merged_range.bounds
                        if cell_coord == f"{openpyxl.utils.get_column_letter(min_col)}{min_row}":
                            colspan = max_col - min_col + 1
                            rowspan = max_row - min_row + 1
                        else:
                            # 如果不是合并单元格的左上角，跳过
                            continue
                        break

                # 处理空值显示
                display_value = str(value) if pd.notna(value) else ''

                # 生成单元格HTML
                cell_attrs = []
                if colspan > 1:
                    cell_attrs.append(f'colspan="{colspan}"')
                if rowspan > 1:
                    cell_attrs.append(f'rowspan="{rowspan}"')

                attrs_str = ' ' + ' '.join(cell_attrs) if cell_attrs else ''
                html += f'<td{attrs_str}>{display_value}</td>'

            html += '</tr>'

        html += '</tbody>'
        html += '</table>'
        html += '</div>'

        # 添加统计信息
        html += f'<div class="mt-3 text-muted">'
        html += f'<small>共 {len(df)} 行 × {len(df.columns)} 列</small>'
        html += '</div>'

        return html

    except ImportError:
        # 如果没有openpyxl，使用简单的表格
        html_table = df.to_html(classes='table table-striped table-bordered table-sm',
                               table_id='preview-table',
                               escape=False)
        return f'<div class="table-responsive" style="max-height: 600px; overflow-y: auto;">{html_table}</div>'
    except Exception as e:
        # 出错时使用简单的表格
        html_table = df.to_html(classes='table table-striped table-bordered table-sm',
                               table_id='preview-table',
                               escape=False)
        return f'<div class="table-responsive" style="max-height: 600px; overflow-y: auto;">{html_table}</div>'

@app.route('/')
def index():
    """主页面 - 重定向到功能清单页面"""
    return redirect('/upload')

@app.route('/upload')
def upload_page():
    """功能清单管理页面"""
    uploaded_files = get_uploaded_files()
    return render_template('upload.html', uploaded_files=uploaded_files)

@app.route('/decompose')
def decompose_page():
    """功能拆解页面"""
    uploaded_files = get_uploaded_files()
    output_files = get_output_files()
    prompt_files = get_prompt_files()
    return render_template('decompose.html',
                         uploaded_files=uploaded_files,
                         output_files=output_files,
                         prompt_files=prompt_files)

@app.route('/validate')
def validate_page():
    """检查修复页面"""
    output_files = get_output_files()
    # 检查是否有修复文件
    has_repair_files = any('_修复_' in file['name'] for file in output_files)
    return render_template('validate.html',
                         output_files=output_files,
                         has_repair_files=has_repair_files)

@app.route('/document')
def document_page():
    """文档生成页面"""
    output_files = get_output_files()
    word_files = get_word_files()
    return render_template('document.html',
                         output_files=output_files,
                         word_files=word_files)

@app.route('/prompts')
def prompts_page():
    """提示词管理页面"""
    prompt_files = get_prompt_files()
    return render_template('prompts.html', prompt_files=prompt_files)

@app.route('/config')
def config_page():
    """系统配置页面"""
    return render_template('config.html')
@app.route('/knowledge')
def knowledge_page():
    """知识库管理页面"""
    return render_template('knowledge.html')

@app.route('/api/knowledge/stats')
def api_kb_stats():
    try:
        from knowledge_base import knowledge_base
        return jsonify(knowledge_base.get_stats())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/knowledge/files')
def api_kb_files():
    """返回当前配置的知识库文件列表及存在性"""
    try:
        import config
        md = [p.strip() for p in config.get_MARKDOWN_MANUAL_PATH().split(',') if p.strip()]
        sql = [p.strip() for p in config.get_SQL_FILE_PATH().split(',') if p.strip()]
        md_info = []
        for item in md:
            parts = item.split(':')
            path = parts[0]
            cp = parts[1] if len(parts)>1 else ''
            md_info.append({'path': path, 'child_pattern': cp, 'exists': os.path.exists(path)})
        sql_info = [{'path': p, 'exists': os.path.exists(p)} for p in sql]
        return jsonify({'markdown': md_info, 'sql': sql_info})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/knowledge/preview', methods=['POST'])
def api_kb_preview():
    try:
        data = request.get_json()
        content = data.get('content','')
        child_pattern = data.get('child_pattern') or config.get_MARKDOWN_CHILD_PATTERN()
        from knowledge_base import knowledge_base
        chunks = knowledge_base.preview_markdown(content, child_pattern)
        return jsonify({'chunks': chunks})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/knowledge/upload', methods=['POST'])
def api_kb_upload():
    """上传知识库文件到data/docs，并更新.env中的变量"""
    try:
        f = request.files.get('file')
        ftype = request.form.get('type')
        child_pattern = request.form.get('child_pattern','')
        if not f or not f.filename:
            return jsonify({'error':'未选择文件'}), 400
        filename = secure_filename(f.filename)
        ext = os.path.splitext(filename)[1].lower()
        if ftype=='markdown' and ext != '.md':
            return jsonify({'error':'请选择.md文件'}), 400
        if ftype=='sql' and ext != '.sql':
            return jsonify({'error':'请选择.sql文件'}), 400
        if ftype=='json' and ext != '.json':
            return jsonify({'error':'请选择.json文件'}), 400

        # 保存到data/docs
        target_dir = os.path.join('data','docs')
        os.makedirs(target_dir, exist_ok=True)
        target_path = os.path.join(target_dir, filename)
        f.save(target_path)

        # 更新.env
        from config_manager import ConfigManager
        cm = ConfigManager(ENV_FILE)
        if ftype=='markdown':
            current = cm.get_str('MARKDOWN_MANUAL_PATH', '')
            entry = f"{target_path}:{child_pattern or ''}".rstrip(':')
            new_val = (current+',' if current else '') + entry
            cm.set_config('MARKDOWN_MANUAL_PATH', new_val)
        else:
            key = 'SQL_FILE_PATH'
            current = cm.get_str(key, '')
            new_val = (current+',' if current else '') + target_path
            cm.set_config(key, new_val)

        return jsonify({'success': True, 'path': target_path})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/knowledge/rebuild', methods=['POST'])
def api_kb_rebuild():
    try:
        from knowledge_base import knowledge_base
        stats = knowledge_base.rebuild()
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/knowledge/search', methods=['POST'])
def api_kb_search():
    try:
        data = request.get_json()
        query = data.get('query','')
        if not query:
            return jsonify({'error':'query不能为空'}), 400
        from knowledge_base import knowledge_base
        res = knowledge_base.search_knowledge(query)
        return jsonify(res)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/files', methods=['GET'])
def get_files():
    """获取所有文件列表API"""
    try:
        uploaded_files = get_uploaded_files()
        output_files = get_output_files()
        word_files = get_word_files()

        return jsonify({
            'success': True,
            'uploaded_files': uploaded_files,
            'output_files': output_files,
            'word_files': word_files
        })
    except Exception as e:
        return jsonify({'error': f'获取文件列表失败: {str(e)}'}), 500

@app.route('/api/prompts', methods=['GET'])
def get_prompts():
    """获取提示词文件列表API"""
    try:
        prompt_files = get_prompt_files()
        return jsonify({
            'success': True,
            'prompts': prompt_files
        })
    except Exception as e:
        return jsonify({'error': f'获取提示词列表失败: {str(e)}'}), 500

@app.route('/api/modules/<folder>/<filename>', methods=['GET'])
def get_modules(folder, filename):
    """获取文件中的模块列表API"""
    try:
        # 根据文件夹类型确定文件路径
        if folder == 'outputs':
            input_file = os.path.join(OUTPUT_FOLDER, filename)
        else:
            input_file = os.path.join(UPLOAD_FOLDER, filename)

        if not os.path.exists(input_file):
            return jsonify({'error': '文件不存在'}), 404

        # 读取Excel文件获取模块信息
        import pandas as pd
        df = pd.read_excel(input_file, sheet_name=1, header=0)

        level_2_name, level_3_name = "二级功能模块", "三级功能模块"

        # 向后填充模块列
        df[[level_2_name, level_3_name]] = df[[level_2_name, level_3_name]].ffill()

        # 获取唯一的二级和三级模块
        level2_modules = df[level_2_name].dropna().astype(str).str.strip().unique().tolist()
        level3_modules = df[level_3_name].dropna().astype(str).str.strip().unique().tolist()

        return jsonify({
            'success': True,
            'level2_modules': level2_modules,
            'level3_modules': level3_modules
        })
    except Exception as e:
        return jsonify({'error': f'获取模块列表失败: {str(e)}'}), 500

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """文件上传API"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        if file and allowed_file(file.filename):
            # 清理文件名，移除或替换非法字符
            #original_filename = file.filename
            filename = clean_filename(file.filename)

            # 先保存到临时位置进行验证
            temp_file_path = os.path.join(UPLOAD_FOLDER, f"temp_{filename}")
            file.save(temp_file_path)
            print(f"保存临时文件: {temp_file_path}")

            # 验证文件格式并识别类型
            is_valid, message, file_type = validate_excel_format(temp_file_path)

            if not is_valid:
                # 删除临时文件
                os.remove(temp_file_path)
                return jsonify({
                    'success': False,
                    'error': message
                }), 400

            # 根据文件类型决定上传目录
            if file_type == "functional_decompose":
                # 功能拆解文件上传到OUTPUT_FOLDER
                target_folder = OUTPUT_FOLDER
                folder_name = "outputs"
            else:
                # 功能清单文件上传到UPLOAD_FOLDER
                target_folder = UPLOAD_FOLDER
                folder_name = "uploads"

            # 检查目标目录中是否已存在同名文件
            if os.path.exists(os.path.join(target_folder, filename)):
                name, ext = os.path.splitext(filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{name}_{timestamp}{ext}"

            # 移动到目标目录
            final_file_path = os.path.join(target_folder, filename)
            os.rename(temp_file_path, final_file_path)

            return jsonify({
                'success': True,
                'message': '文件上传成功',
                'filename': filename,
                'original_filename': file.filename,  # 添加原始文件名
                'file_type': file_type,
                'folder': folder_name,
                'validation': {
                    'valid': is_valid,
                    'message': message,
                    'file_type': file_type
                }
            })
        else:
            return jsonify({'error': '不支持的文件格式'}), 400

    except Exception as e:
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@app.route('/api/delete/<folder>/<filename>', methods=['DELETE'])
def delete_file(folder, filename):
    """删除文件"""
    try:
        folder_map = {
            'uploads': UPLOAD_FOLDER,
            'outputs': OUTPUT_FOLDER,
            'words': WORDS_FOLDER
        }

        if folder not in folder_map:
            return jsonify({'error': '无效的文件夹'}), 400

        file_path = os.path.join(folder_map[folder], filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            return jsonify({'success': True, 'message': '文件删除成功'})
        else:
            return jsonify({'error': '文件不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

@app.route('/api/download/<folder>/<filename>')
def download_file(folder, filename):
    """下载文件"""
    try:
        folder_map = {
            'uploads': UPLOAD_FOLDER,
            'outputs': OUTPUT_FOLDER,
            'words': WORDS_FOLDER
        }

        if folder not in folder_map:
            return jsonify({'error': '无效的文件夹'}), 400

        file_path = os.path.join(folder_map[folder], filename)
        if os.path.exists(file_path):
            file_abs_path = os.path.abspath(file_path)
            return send_file(file_abs_path, as_attachment=True, download_name=filename)
        else:
            return jsonify({'error': '文件不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

# @app.route('/api/preview/<folder>/<filename>')
# def preview_file(folder, filename):
#     """预览文件"""
#     try:
#         folder_map = {
#             'uploads': UPLOAD_FOLDER,
#             'outputs': OUTPUT_FOLDER,
#             'words': WORDS_FOLDER
#         }

#         if folder not in folder_map:
#             return jsonify({'error': '无效的文件夹'}), 400

#         file_path = os.path.join(folder_map[folder], filename)
#         if not os.path.exists(file_path):
#             return jsonify({'error': '文件不存在'}), 404

#         # 根据文件类型返回预览
#         if filename.lower().endswith(('.xlsx', '.xls')):
#             return render_template('preview.html',
#                                  file_path=file_path,
#                                  filename=filename,
#                                  file_type='excel')
#         elif filename.lower().endswith('.md'):
#             return render_template('preview.html',
#                                  file_path=file_path,
#                                  filename=filename,
#                                  file_type='markdown')
#         else:
#             return jsonify({'error': '不支持预览此文件类型'}), 400
#     except Exception as e:
#         return jsonify({'error': f'预览失败: {str(e)}'}), 500

@app.route('/api/cosmic_decompose', methods=['POST'])
def cosmic_decompose():
    """COSMIC功能拆解API"""
    try:
        data = request.get_json()
        filename = data.get('filename')
        folder = data.get('folder', 'uploads')  # 新增文件夹参数
        prompt_file = data.get('prompt_file', 'prompt/prompt.md')
        level2_module = data.get('level2_module')
        level3_module = data.get('level3_module')

        if not filename:
            return jsonify({'error': '未指定文件'}), 400

        # 根据文件夹类型确定文件路径
        if folder == 'outputs':
            input_file = os.path.join(OUTPUT_FOLDER, filename)
        else:
            input_file = os.path.join(UPLOAD_FOLDER, filename)

        if not os.path.exists(input_file):
            return jsonify({'error': '文件不存在'}), 404

        # 生成输出文件名
        base_name = os.path.splitext(filename)[0]
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 根据模块选择调整文件名
        suffix_parts = ['拆解']
        if level2_module:
            suffix_parts.append(f'L2_{level2_module}')
        if level3_module:
            suffix_parts.append(f'L3_{level3_module}')
        suffix_parts.append(timestamp)

        output_filename = f"{base_name}_{'_'.join(suffix_parts)}.xlsx"
        output_file = os.path.join(OUTPUT_FOLDER, output_filename)

        # 调用拆解功能
        start_time = time.time()
        result = do_cosmic_file(input_file, output_file, prompt_file, level2_module, level3_module)
        # 计算耗时 (秒)
        time_cost = time.time() - start_time
        time_cost = f"耗时: {time_cost:.2f} 秒"

        if os.path.exists(output_file):
            return jsonify({
                'success': True,
                'message': 'COSMIC功能拆解完成,' + time_cost,
                'output_filename': output_filename
            })
        else:
            return jsonify({'error': 'COSMIC功能拆解失败'}), 500

    except ValueError as e:
        # 处理重复检查等验证错误
        error_msg = str(e)
        if "发现重复内容" in error_msg:
            return jsonify({
                'error': '数据验证失败',
                'message': error_msg,
                'type': 'duplicate_error'
            }), 400
        else:
            return jsonify({'error': f'数据验证失败: {error_msg}'}), 400
    except Exception as e:
        return jsonify({'error': f'拆解失败: {str(e)}'}), 500

@app.route('/api/validate_repair', methods=['POST'])
def validate_repair():
    """检查和修复COSMIC拆解文件"""
    try:
        data = request.get_json()
        filename = data.get('filename')
        folder = data.get('folder', 'outputs')  # 新增文件夹参数，默认为outputs
        check_items = data.get('check_items', ["movement","english","llm","duplicate","long"])
        check_fix = data.get('check_fix', True)

        if not filename:
            return jsonify({'error': '未指定文件'}), 400

        # 根据文件夹类型确定文件路径
        if folder == 'outputs':
            input_file = os.path.join(OUTPUT_FOLDER, filename)
        else:
            input_file = os.path.join(UPLOAD_FOLDER, filename)

        if not os.path.exists(input_file):
            return jsonify({'error': '文件不存在'}), 404

        # 生成修复文件名（仅在需要修复时）
        repair_file = None
        repair_filename = None
        if check_fix:
            base_name = os.path.splitext(filename)[0]
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            repair_filename = f"{base_name}_修复_{timestamp}.xlsx"
            repair_file = os.path.join(OUTPUT_FOLDER, repair_filename)

        # 调用校验修复功能
        start_time = time.time()
        validator = CosmicValidator()
        result = validator.validate_file(input_file, repair_file, check_items, check_fix)
        repair_time = time.time() - start_time
        repair_time = f"耗时: {repair_time:.2f} 秒"


        if check_fix:
            # 修复模式：检查修复文件是否生成
            if repair_file and os.path.exists(repair_file):
                return jsonify({
                    'success': True,
                    'message': '检查修复完成,' + repair_time,
                    'repair_filename': repair_filename,
                    'report': result
                })
            else:
                return jsonify({'error': '检查修复失败'}), 500
        else:
            # 仅检查模式：直接返回检查结果
            return jsonify({
                'success': True,
                'message': '检查完成',
                'report': result
            })

    except Exception as e:
        return jsonify({'error': f'检查修复失败: {str(e)}'}), 500

@app.route('/api/generate_document', methods=['POST'])
def generate_document():
    """生成Word文档"""
    try:
        data = request.get_json()
        filename = data.get('filename')
        folder = data.get('folder', 'outputs')  # 新增文件夹参数，默认为outputs

        if not filename:
            return jsonify({'error': '未指定文件'}), 400

        # 根据文件夹类型确定文件路径
        if folder == 'outputs':
            input_file = os.path.join(OUTPUT_FOLDER, filename)
        else:
            input_file = os.path.join(UPLOAD_FOLDER, filename)

        if not os.path.exists(input_file):
            return jsonify({'error': '文件不存在'}), 404

        # 生成markdown文件名
        base_name = os.path.splitext(filename)[0]
        md_filename = f"{base_name}_功能需求文档.md"
        md_file = os.path.join(WORDS_FOLDER, md_filename)

        # 调用文档生成功能
        start_time = time.time()
        generator = RequirementGenerator()
        result = generator.generate_requirements_document(input_file, md_file)
        doc_time = time.time() - start_time
        doc_time = f"耗时: {doc_time:.2f} 秒"

        if os.path.exists(md_file):
            return jsonify({
                'success': True,
                'message': '文档生成完成, ' + doc_time,
                'md_filename': md_filename
            })
        else:
            return jsonify({'error': '文档生成失败'}), 500

    except Exception as e:
        return jsonify({'error': f'文档生成失败: {str(e)}'}), 500

@app.route('/api/convert_to_word', methods=['POST'])
def convert_to_word():
    """将Markdown转换为Word文档"""
    try:
        data = request.get_json()
        filename = data.get('filename')

        if not filename:
            return jsonify({'error': '未指定文件'}), 400

        md_file = os.path.join(WORDS_FOLDER, filename)
        if not os.path.exists(md_file):
            return jsonify({'error': '文件不存在'}), 404

        # 调用转换功能
        word_file = md2docx(md_file)

        if word_file and os.path.exists(word_file):
            # word_path = os.path.abspath(word_file)
            # return send_file(word_path, as_attachment=True)
            return jsonify({
                'success': True,
                'message': '文档已转为Word!',
                'output_file': word_file
            })
        else:
            return jsonify({'error': 'Word转换失败'}), 500

    except Exception as e:
        return jsonify({'error': f'转换失败: {str(e)}'}), 500

@app.route('/api/delete_output/<filename>', methods=['DELETE'])
def delete_output_file(filename):
    """删除输出文件"""
    try:
        file_path = os.path.join(OUTPUT_FOLDER, filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            return jsonify({'success': True, 'message': '文件删除成功'})
        else:
            return jsonify({'error': '文件不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

@app.route('/api/delete_word/<filename>', methods=['DELETE'])
def delete_word_file(filename):
    """删除Word文档文件"""
    try:
        file_path = os.path.join(WORDS_FOLDER, filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            return jsonify({'success': True, 'message': '文件删除成功'})
        else:
            return jsonify({'error': '文件不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

# @app.route('/download/<path:folder>/<filename>')
# def download_file(folder, filename):
#     """下载文件"""
#     try:
#         if folder == 'uploads':
#             file_path = os.path.join(UPLOAD_FOLDER, filename)
#         elif folder == 'outputs':
#             file_path = os.path.join(OUTPUT_FOLDER, filename)
#         elif folder == 'words':
#             file_path = os.path.join(WORDS_FOLDER, filename)
#         else:
#             return jsonify({'error': '无效的文件夹'}), 400

#         if os.path.exists(file_path):
#             return send_file(file_path, as_attachment=True)
#         else:
#             return jsonify({'error': '文件不存在'}), 404
#     except Exception as e:
#         return jsonify({'error': f'下载失败: {str(e)}'}), 500

@app.route('/api/preview/<path:folder>/<filename>')
def preview_file(folder, filename):
    """预览文件"""
    try:
        if folder == 'uploads':
            file_path = os.path.join(UPLOAD_FOLDER, filename)
        elif folder == 'outputs':
            file_path = os.path.join(OUTPUT_FOLDER, filename)
        elif folder == 'words':
            file_path = os.path.join(WORDS_FOLDER, filename)
        else:
            return jsonify({'error': '无效的文件夹'}), 400

        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404

        # 根据文件类型处理预览
        if filename.endswith('.md'):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            html_content = render_markdown_with_mermaid(content)
            return html_content
        elif filename.endswith(('.xlsx', '.xls')):
            # Excel文件预览 - 显示所有行并支持合并单元格
            df = llm_util.parse_excel_file(file_path)

            # 生成带样式的HTML表格
            html_table = generate_excel_preview_html(file_path, df)
            return render_template('preview.html', content=html_table, filename=filename)
        else:
            return jsonify({'error': '不支持的文件类型预览'}), 400

    except Exception as e:
        return jsonify({'error': f'预览失败: {str(e)}'}), 500

@app.route('/api/preview_markdown', methods=['POST'])
def preview_markdown():
    """预览Markdown内容，支持Mermaid渲染"""
    try:
        data = request.get_json()
        content = data.get('content', '')

        if not content.strip():
            return '<html><body><p>内容为空</p></body></html>'

        # 使用markdown渲染
        html_content = markdown.markdown(content, extensions=['tables', 'fenced_code'])

        # 使用document_template.html渲染
        return render_template('document_template.html', html_content=html_content)

    except Exception as e:
        return f'<html><body><h1>预览失败</h1><p>{str(e)}</p></body></html>'

@app.route('/api/prompt/<path:filename>')
def get_prompt_content(filename):
    """获取提示词内容"""
    try:
        if not os.path.exists(filename):
            return jsonify({'error': '文件不存在'}), 404

        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()

        return jsonify({
            'success': True,
            'content': content,
            'filename': filename
        })

    except Exception as e:
        return jsonify({'error': f'读取失败: {str(e)}'}), 500

@app.route('/api/prompt/<path:filename>', methods=['POST'])
def save_prompt_content(filename):
    """保存提示词内容"""
    try:
        data = request.get_json()
        content = data.get('content', '')

        if not content.strip():
            return jsonify({'error': '内容不能为空'}), 400

        # 备份原文件
        if os.path.exists(filename):
            backup_filename = f"{filename}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(filename, backup_filename)

        # 保存新内容
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        return jsonify({
            'success': True,
            'message': '提示词保存成功',
            'filename': filename
        })

    except Exception as e:
        return jsonify({'error': f'保存失败: {str(e)}'}), 500

@app.route('/api/prompt/save-as', methods=['POST'])
def save_prompt_as():
    """另存提示词内容为新文件"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        new_filename = data.get('filename', '')

        if not content.strip():
            return jsonify({'error': '内容不能为空'}), 400

        if not new_filename.strip():
            return jsonify({'error': '文件名不能为空'}), 400

        # 确保文件名以prompt开头并以.md结尾
        if not new_filename.startswith('prompt'):
            new_filename = f"prompt_{new_filename}"

        if not new_filename.endswith('.md'):
            new_filename = f"{new_filename}.md"

        # 构建完整路径
        full_path = os.path.join('prompt', new_filename)

        # 检查文件是否已存在
        if os.path.exists(full_path):
            return jsonify({'error': '文件名已存在，请选择其他名称'}), 400

        # 确保prompt目录存在
        os.makedirs('prompt', exist_ok=True)

        # 保存新文件
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return jsonify({
            'success': True,
            'message': '提示词另存成功',
            'filename': new_filename,
            'full_path': full_path
        })

    except Exception as e:
        return jsonify({'error': f'另存失败: {str(e)}'}), 500

@app.route('/api/config')
def get_config():
    """获取配置信息"""
    try:
        from config_manager import ConfigManager
        config_manager = ConfigManager(ENV_FILE)
        config_groups = config_manager.get_config_groups()

        # 将字典格式转换为前端期望的数组格式
        if isinstance(config_groups, dict):
            formatted_groups = []
            for group_name, group_configs in config_groups.items():
                group = {
                    'title': group_name,
                    'description': f'{group_name}相关配置',
                    'configs': []
                }

                for config_key, config_info in group_configs.items():
                    config_item = {
                        'key': config_key,
                        'display_name': config_info.get('description', config_key),
                        'description': config_info.get('description', ''),
                        'type': config_info.get('type', 'text'),
                        'value': config_info.get('value', ''),
                        'options': config_info.get('options', []),
                        'min': config_info.get('min'),
                        'max': config_info.get('max'),
                        'step': config_info.get('step')
                    }

                    # 为options添加label属性
                    if config_item['options'] and isinstance(config_item['options'], list):
                        config_item['options'] = [
                            {'value': opt, 'label': opt} if isinstance(opt, str) else opt
                            for opt in config_item['options']
                        ]

                    group['configs'].append(config_item)

                formatted_groups.append(group)

            return jsonify({
                'success': True,
                'config_groups': formatted_groups
            })
        else:
            return jsonify({
                'error': '配置组数据格式错误'
            }), 500

    except Exception as e:
        return jsonify({'error': f'获取配置失败: {str(e)}'}), 500

@app.route('/api/config', methods=['POST'])
def save_config():
    """保存配置信息"""
    try:
        data = request.get_json()
        configs = data.get('configs', {})

        from config_manager import ConfigManager
        config_manager = ConfigManager(ENV_FILE)

        # 验证配置
        validation_errors = {}
        for key, value in configs.items():
            is_valid, error_msg = config_manager.validate_config(key, value)
            if not is_valid:
                validation_errors[key] = error_msg

        if validation_errors:
            return jsonify({
                'success': False,
                'error': '配置验证失败',
                'validation_errors': validation_errors
            }), 400

        # 更新配置
        results = config_manager.update_configs(configs)
        failed_configs = [key for key, success in results.items() if not success]

        if failed_configs:
            return jsonify({
                'success': False,
                'error': f'以下配置更新失败: {", ".join(failed_configs)}'
            }), 500

        return jsonify({
            'success': True,
            'message': '配置保存成功'
        })

    except Exception as e:
        return jsonify({'error': f'保存配置失败: {str(e)}'}), 500

@app.route('/api/config/export')
def export_config():
    """导出配置"""
    try:
        from config_manager import ConfigManager
        config_manager = ConfigManager(ENV_FILE)
        content = config_manager.export_config()

        return jsonify({
            'success': True,
            'content': content
        })
    except Exception as e:
        return jsonify({'error': f'导出配置失败: {str(e)}'}), 500

@app.route('/api/config/import', methods=['POST'])
def import_config():
    """导入配置"""
    try:
        data = request.get_json()
        content = data.get('content', '')

        if not content:
            return jsonify({'error': '配置内容不能为空'}), 400

        from config_manager import ConfigManager
        config_manager = ConfigManager(ENV_FILE)
        success = config_manager.import_config(content)

        if success:
            return jsonify({
                'success': True,
                'message': '配置导入成功'
            })
        else:
            return jsonify({'error': '配置导入失败'}), 500

    except Exception as e:
        return jsonify({'error': f'导入配置失败: {str(e)}'}), 500

@app.route('/api/config/<key>/reset', methods=['POST'])
def reset_config(key):
    """重置配置为默认值"""
    try:
        from config_manager import ConfigManager
        config_manager = ConfigManager(ENV_FILE)
        success = config_manager.reset_to_default(key)

        if success:
            return jsonify({
                'success': True,
                'message': f'配置 {key} 已重置为默认值'
            })
        else:
            return jsonify({'error': f'重置配置 {key} 失败'}), 500

    except Exception as e:
        return jsonify({'error': f'重置配置失败: {str(e)}'}), 500

if __name__ == '__main__':
    port=5002
    print("=== COSMIC功能拆解系统 Web UI ===")
    app.run(debug=False, host='0.0.0.0', port=port)
