FROM python:3.11

# 设置工作目录
WORKDIR /cosmic-ai

# 更新源并安装依赖
RUN tee /etc/apt/sources.list <<'EOF'
deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm          main contrib non-free non-free-firmware
deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-updates main contrib non-free non-free-firmware
deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-backports main contrib non-free non-free-firmware
deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main contrib non-free non-free-firmware
EOF

RUN rm /etc/apt/sources.list.d/* && \
    apt update && \
    apt install -y pandoc

# 安装playwright及其依赖
RUN pip install playwright -i https://mirrors.aliyun.com/pypi/simple/ && \
    playwright install-deps && \
    playwright install chromium

# 复制依赖文件并安装Python依赖
COPY requirements.txt .
RUN pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 复制项目源文件到镜像内
COPY .env /cosmic-ai/
COPY src/ /cosmic-ai/src/
COPY prompt/ /cosmic-ai/prompt/
COPY static/ /cosmic-ai/static/
COPY templates/ /cosmic-ai/templates/

# 创建data目录并设置卷映射
RUN mkdir -p /cosmic-ai/data
VOLUME ["/cosmic-ai/data", "/cosmic-ai/.env"]

# 暴露端口
EXPOSE 5002

# 启动命令
CMD ["python", "src/web_ui.py"]