<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库管理 - COSMIC系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar { min-height: 100vh; background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); }
        .nav-link { color: rgba(255,255,255,0.8); border-radius: 8px; margin: 2px 0; }
        .nav-link:hover, .nav-link.active { color: white; background-color: rgba(255,255,255,0.1); }
        .content-area { background-color: #f8f9fa; min-height: 100vh; }
        .file-list li { font-family: monospace; }
        .preview-box { max-height: 240px; overflow: auto; background: #fff; border: 1px solid #eee; padding: 10px; border-radius: 6px; }
        .small-muted { font-size: 12px; color: #6c757d; }
        .code { font-family: monospace; }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="row">
        <!-- 左侧导航栏 -->
        <div class="col-md-3 col-lg-2 sidebar p-3">
            <div class="text-center mb-4">
                <h4 class="text-white">COSMIC系统</h4>
                <small class="text-white-50">功能拆解与文档生成</small>
            </div>
            <nav class="nav flex-column">
                <a class="nav-link" href="/upload"><i class="bi bi-cloud-upload me-2"></i>文件管理</a>
                <a class="nav-link" href="/decompose"><i class="bi bi-diagram-3 me-2"></i>功能拆解</a>
                <a class="nav-link" href="/validate"><i class="bi bi-check-circle me-2"></i>检查修复</a>
                <a class="nav-link" href="/document"><i class="bi bi-file-text me-2"></i>文档生成</a>
                <a class="nav-link" href="/prompts"><i class="bi bi-pencil-square me-2"></i>提示词管理</a>
                <a class="nav-link" href="/config"><i class="bi bi-gear me-2"></i>系统配置</a>
                <a class="nav-link active" href="/knowledge"><i class="bi bi-journal-text me-2"></i>知识库管理</a>
            </nav>
        </div>
        <!-- 主内容区域 -->
        <div class="col-md-9 col-lg-10 content-area p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-journal-text me-2"></i>知识库管理</h2>
                <button class="btn btn-outline-secondary" onclick="loadStats()"><i class="bi bi-arrow-clockwise me-1"></i>刷新</button>
            </div>

            <div class="row g-3">
                <!-- 统计与控制 -->
                <div class="col-lg-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">知识库状态</h5>
                            <div id="kb-stats">
                                <div class="small-muted">加载中...</div>
                            </div>
                            <div class="mt-3 d-grid">
                                <button class="btn btn-primary" onclick="rebuildKB()"><i class="bi bi-arrow-repeat me-1"></i>重建知识库</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件配置 -->
                <div class="col-lg-8">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">已配置文件</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Markdown 手册</h6>
                                    <ul id="md-files" class="file-list small"></ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>SQL/JSON 数据</h6>
                                    <ul id="sql-files" class="file-list small"></ul>
                                </div>
                            </div>
                            <hr>
                            <h5 class="card-title">上传文件</h5>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">文件类型</label>
                                    <select id="fileType" class="form-select">
                                        <option value="markdown">Markdown (.md)</option>
                                        <option value="sql">SQL (.sql)</option>
                                        <option value="json">JSON (.json)</option>
                                    </select>
                                </div>
                                <div class="col-md-8">
                                    <label class="form-label">选择文件</label>
                                    <input id="kbFile" type="file" class="form-control" accept=".md,.sql,.json">
                                </div>
                                <div class="col-12" id="mdOptions">
                                    <label class="form-label">分块符（Markdown 子标题层级）</label>
                                    <div class="input-group">
                                        <input id="childPattern" class="form-control" value="####" placeholder="例如 #### 或 ### 或 ##">
                                        <button class="btn btn-outline-secondary" type="button" onclick="previewMarkdown()">预览分块</button>
                                    </div>
                                    <div class="small-muted mt-1">不填写则使用系统默认分块符（配置项 MARKDOWN_CHILD_PATTERN）。</div>
                                    <div class="mt-2 preview-box small" id="previewBox" style="display:none"></div>
                                </div>
                                <div class="col-12 d-grid">
                                    <button class="btn btn-success" onclick="uploadKBFile()"><i class="bi bi-upload me-1"></i>上传并更新配置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row g-3 mt-3">
                <!-- 知识检索测试 -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">知识检索测试</h5>
                            <div class="input-group mb-2">
                                <input id="kbQuery" class="form-control" placeholder="输入模块/关键词，例如：证书发放 CSR 生成">
                                <button class="btn btn-outline-primary" onclick="searchKB()">检索</button>
                            </div>
                            <div id="kbSearchResult" class="preview-box small" style="display:none"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function alertMsg(type, msg){
    const cls = type==='error'?'danger':(type==='success'?'success':'warning');
    const el = document.createElement('div');
    el.className = `alert alert-${cls}`;
    el.textContent = msg;
    document.body.appendChild(el);
    setTimeout(()=>el.remove(), 2000);
}

async function loadStats(){
    try{
        const [statsRes, filesRes] = await Promise.all([
            fetch('/api/knowledge/stats'),
            fetch('/api/knowledge/files')
        ]);
        const stats = await statsRes.json();
        const files = await filesRes.json();
        const kb = document.getElementById('kb-stats');
        kb.innerHTML = `
            <div>启用：<b>${stats.enabled ? '是' : '否'}</b></div>
            <div>后端：<b>${stats.use_chromadb ? 'ChromaDB' : 'TF-IDF'}</b></div>
            <div>功能说明数量：<b>${stats.function_docs || 0}</b></div>
            <div>数据实体数量：<b>${stats.entity_docs || 0}</b></div>
            <div class="small-muted">缓存目录：<span class="code">${stats.cache_dir || ''}</span></div>
        `;
        const mdUl = document.getElementById('md-files');
        const sqlUl = document.getElementById('sql-files');
        mdUl.innerHTML = '';
        (files.markdown||[]).forEach(it=>{
            const li = document.createElement('li');
            li.textContent = `${it.path}${it.child_pattern?(':'+it.child_pattern):''} ${it.exists?'':'(不存在)'}`;
            mdUl.appendChild(li);
        });
        sqlUl.innerHTML = '';
        (files.sql||[]).forEach(it=>{
            const li = document.createElement('li');
            li.textContent = `${it.path} ${it.exists?'':'(不存在)'}`;
            sqlUl.appendChild(li);
        });
    }catch(e){ alertMsg('error', '加载知识库信息失败'); }
}

function toggleMdOptions(){
    document.getElementById('mdOptions').style.display = (document.getElementById('fileType').value==='markdown')?'block':'none';
}

document.getElementById('fileType').addEventListener('change', toggleMdOptions);
setTimeout(toggleMdOptions, 0);

async function previewMarkdown(){
    const file = document.getElementById('kbFile').files[0];
    if(!file){ return alertMsg('warn', '请先选择Markdown文件'); }
    if(!file.name.endsWith('.md')){ return alertMsg('warn','请选择.md文件'); }
    const text = await file.text();
    const childPattern = document.getElementById('childPattern').value || '####';
    const res = await fetch('/api/knowledge/preview', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ content:text, child_pattern: childPattern })});
    const data = await res.json();
    const box = document.getElementById('previewBox');
    if(data.error){ box.style.display='block'; box.textContent = '预览失败：'+data.error; return; }
    const chunks = data.chunks||[];
    box.style.display='block';
    box.innerHTML = chunks.slice(0,50).map((c,i)=>`${i+1}. ${c.title} (${c.length}字)`).join('<br>') || '无切块';
}

async function uploadKBFile(){
    const file = document.getElementById('kbFile').files[0];
    if(!file){ return alertMsg('warn','请选择文件'); }
    const type = document.getElementById('fileType').value;
    const form = new FormData();
    form.append('file', file);
    form.append('type', type);
    if(type==='markdown'){
        form.append('child_pattern', document.getElementById('childPattern').value || '');
    }
    const res = await fetch('/api/knowledge/upload', { method:'POST', body: form });
    const data = await res.json();
    if(data.error){ return alertMsg('error', data.error); }
    alertMsg('success','上传成功，配置已更新');
    loadStats();
}

async function rebuildKB(){
    const res = await fetch('/api/knowledge/rebuild', { method:'POST' });
    const data = await res.json();
    if(data.error){ return alertMsg('error', data.error); }
    alertMsg('success','知识库重建完成');
    loadStats();
}

async function searchKB(){
    const q = document.getElementById('kbQuery').value.trim();
    if(!q){ return alertMsg('warn','请输入查询内容'); }
    const res = await fetch('/api/knowledge/search', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ query:q })});
    const data = await res.json();
    const box = document.getElementById('kbSearchResult');
    box.style.display='block';
    if(data.error){ box.textContent = '检索失败：'+data.error; return; }
    const m = data.manual_results||[]; const s = data.sql_results||[];
    box.innerHTML = `<b>手册结果（前${m.length}）：</b><br>`+ m.map((r,i)=>`${i+1}. ${r.title} [${(r.similarity||0).toFixed(3)}]`).join('<br>')
        +'<hr><b>实体结果（前'+s.length+'）：</b><br>' + s.map((r,i)=>`${i+1}. ${r.table_name||''} [${(r.similarity||0).toFixed(3)}]`).join('<br>');
}

loadStats();
</script>
</body>
</html>

