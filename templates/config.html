<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统配置 - COSMIC功能拆解系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 8px;
            margin: 2px 0;
        }
        .nav-link:hover, .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .content-area {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 12px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h4 class="text-white">COSMIC系统</h4>
                    <small class="text-white-50">功能拆解与文档生成</small>
                </div>

                <nav class="nav flex-column">
                    <a class="nav-link" href="/upload">
                        <i class="bi bi-cloud-upload me-2"></i>文件管理
                    </a>
                    <a class="nav-link" href="/decompose">
                        <i class="bi bi-diagram-3 me-2"></i>功能拆解
                    </a>
                    <a class="nav-link" href="/validate">
                        <i class="bi bi-check-circle me-2"></i>检查修复
                    </a>
                    <a class="nav-link" href="/document">
                        <i class="bi bi-file-text me-2"></i>文档生成
                    </a>
                    <a class="nav-link" href="/prompts">
                        <i class="bi bi-pencil-square me-2"></i>提示词管理
                    </a>
                    <a class="nav-link active" href="/config">
                        <i class="bi bi-gear me-2"></i>系统配置
                    </a>
                    <a class="nav-link" href="/knowledge">
                        <i class="bi bi-journal-text me-2"></i>知识库管理
                    </a>

                </nav>
            </div>

            <!-- 主内容区域 -->
            <div class="col-md-9 col-lg-10 content-area p-4">
                <!-- 系统配置区域 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-gear me-2"></i>系统配置</h2>
                    <div>
                        <button class="btn btn-outline-primary me-2" onclick="loadConfig()">
                            <i class="bi bi-arrow-clockwise me-1"></i>刷新配置
                        </button>
                        <button class="btn btn-success me-2" onclick="saveConfig()">
                            <i class="bi bi-save me-1"></i>保存配置
                        </button>
                        <button class="btn btn-outline-secondary me-2" onclick="exportConfig()">
                            <i class="bi bi-download me-1"></i>导出配置
                        </button>
                        <button class="btn btn-outline-info" onclick="importConfig()">
                            <i class="bi bi-upload me-1"></i>导入配置
                        </button>
                    </div>
                </div>

                <div id="configContainer">
                    <!-- 配置内容将通过JavaScript动态生成 -->
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载配置...</p>
                    </div>
                </div>

                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>注意：</strong>修改配置后需要重启系统才能生效。请谨慎修改API密钥等敏感信息。
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入配置模态框 -->
    <div class="modal fade" id="importConfigModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导入配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">配置内容</label>
                        <textarea class="form-control" id="importConfigContent" rows="15" placeholder="请粘贴配置内容..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmImportConfig()">导入</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/config.js"></script>
</body>
</html>
