<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词管理 - COSMIC功能拆解系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 8px;
            margin: 2px 0;
        }
        .nav-link:hover, .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .content-area {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 12px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h4 class="text-white">COSMIC系统</h4>
                    <small class="text-white-50">功能拆解与文档生成</small>
                </div>

                <nav class="nav flex-column">
                    <a class="nav-link" href="/upload">
                        <i class="bi bi-cloud-upload me-2"></i>文件管理
                    </a>
                    <a class="nav-link" href="/decompose">
                        <i class="bi bi-diagram-3 me-2"></i>功能拆解
                    </a>
                    <a class="nav-link" href="/validate">
                        <i class="bi bi-check-circle me-2"></i>检查修复
                    </a>
                    <a class="nav-link" href="/document">
                        <i class="bi bi-file-text me-2"></i>文档生成
                    </a>
                    <a class="nav-link active" href="/prompts">
                        <i class="bi bi-pencil-square me-2"></i>提示词管理
                    </a>
                    <a class="nav-link" href="/knowledge">
                        <i class="bi bi-journal-text me-2"></i>知识库管理
                    </a>
                    <a class="nav-link" href="/config">
                        <i class="bi bi-gear me-2"></i>系统配置
                    </a>
                </nav>
            </div>

            <!-- 主内容区域 -->
            <div class="col-md-9 col-lg-10 content-area p-4">
                <!-- 提示词管理区域 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-pencil-square me-2"></i>COSMIC拆解提示词管理</h2>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">编辑COSMIC拆解提示词</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">选择提示词文件</label>
                                <select class="form-select" id="promptEditSelect" onchange="loadPromptContent()">
                                    <option value="">请选择提示词文件...</option>
                                    {% for prompt in prompt_files %}
                                    <option value="{{ prompt.path }}" data-is-system="{{ prompt.is_system|default(false)|lower }}">{{ prompt.display_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button class="btn btn-primary me-2" onclick="savePromptContent()">
                                    <i class="bi bi-save me-1"></i>保存
                                </button>
                                <button class="btn btn-success me-2" id="saveAsBtn" onclick="savePromptAs()">
                                    <i class="bi bi-save-fill me-1"></i>另存为
                                </button>
                                <button class="btn btn-outline-secondary" onclick="resetPromptContent()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>重置
                                </button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label">提示词内容</label>
                                <div class="btn-group btn-group-sm" role="group">
                                    <input type="radio" class="btn-check" name="promptViewMode" id="editMode" autocomplete="off" checked>
                                    <label class="btn btn-outline-primary" for="editMode">编辑</label>

                                    <input type="radio" class="btn-check" name="promptViewMode" id="previewMode" autocomplete="off">
                                    <label class="btn btn-outline-primary" for="previewMode">预览</label>
                                </div>
                            </div>

                            <div id="promptEditArea">
                                <textarea class="form-control" id="promptContent" rows="20" placeholder="请选择提示词文件后编辑内容..."></textarea>
                            </div>

                            <div id="promptPreviewArea" style="display: none;">
                                <div class="border rounded p-3" style="min-height: 500px; background-color: #f8f9fa;">
                                    <div id="promptPreviewContent">
                                        <p class="text-muted">请先编辑内容，然后切换到预览模式查看效果</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>提示：</strong>此功能仅用于编辑COSMIC功能拆解相关的提示词文件。修改后的提示词将在下次拆解时生效。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/prompts.js"></script>
</body>
</html>
