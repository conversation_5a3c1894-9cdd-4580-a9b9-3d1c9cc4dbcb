<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COSMIC功能拆解系统-文件管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 8px;
            margin: 2px 0;
        }
        .nav-link:hover, .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .content-area {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 3rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0d6efd;
            background-color: rgba(13, 110, 253, 0.05);
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: rgba(13, 110, 253, 0.1);
        }
        .file-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: white;
            transition: all 0.2s ease;
        }
        .file-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .btn-group-sm .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
        .clickable-upload {
            cursor: pointer;
            transition: color 0.2s ease;
        }
        .clickable-upload:hover {
            color: #0d6efd !important;
        }
        .upload-area .bi-cloud-upload,
        .upload-area h5,
        .upload-area p.text-muted {
            cursor: pointer;
            transition: color 0.2s ease;
        }
        .upload-area .bi-cloud-upload:hover,
        .upload-area h5:hover,
        .upload-area p.text-muted:hover {
            color: #0d6efd !important;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h4 class="text-white">COSMIC系统</h4>
                    <small class="text-white-50">功能拆解与文档生成</small>
                </div>

                <nav class="nav flex-column">
                    <a class="nav-link active" href="/upload">
                        <i class="bi bi-cloud-upload me-2"></i>文件管理
                    </a>
                    <a class="nav-link" href="/decompose">
                        <i class="bi bi-diagram-3 me-2"></i>功能拆解
                    </a>
                    <a class="nav-link" href="/validate">
                        <i class="bi bi-check-circle me-2"></i>检查修复
                    </a>
                    <a class="nav-link" href="/document">
                        <i class="bi bi-file-text me-2"></i>文档生成
                    </a>
                    <a class="nav-link" href="/prompts">
                        <i class="bi bi-pencil-square me-2"></i>提示词管理
                    </a>
                    <a class="nav-link" href="/config">
                        <i class="bi bi-gear me-2"></i>系统配置
                    </a>
                        <a class="nav-link" href="/knowledge">
                            <i class="bi bi-journal-text me-2"></i>知识库管理
                        </a>

                </nav>
            </div>

            <!-- 主内容区域 -->
            <div class="col-md-9 col-lg-10 content-area p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-cloud-upload me-2"></i>文件管理</h2>
                    <button class="btn btn-primary" onclick="refreshFiles()">
                        <i class="bi bi-arrow-clockwise me-1"></i>刷新
                    </button>
                </div>

                <!-- 上传区域 -->
                <div class="card mb-4">
                    <div class="card-body">
                            <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
                            <button class="btn btn-outline-primary mt-3" onclick="selectFile(event)">
                                选择文件（支持 .xlsx, .xls 格式，最大 16MB）
                            </button>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="card border-primary">
                                        <div class="card-body text-center">
                                            <h6 class="text-primary"><i class="bi bi-list-check me-2"></i>功能清单文件</h6>
                                            <small class="text-muted">包含：一级功能模块、二级功能模块、三级功能模块、功能过程、功能描述、预估工作量（人天）</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-body text-center">
                                            <h6 class="text-success"><i class="bi bi-diagram-3 me-2"></i>功能拆解文件</h6>
                                            <small class="text-muted">包含：一级功能模块、二级功能模块、三级功能模块、功能用户、触发事件、功能过程、子过程描述、数据移动类型、数据组、数据属性、CFP</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <div id="uploadProgress" class="mt-3" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 已上传文件列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">已上传文件 (<span id="uploadedFilesCount">0</span>)</h5>
                    </div>
                    <div class="card-body">
                        <div id="uploadedFilesList">
                            <!-- 文件列表将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/upload.js"></script>
</body>
</html>
