<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能拆解 - COSMIC功能拆解系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 8px;
            margin: 2px 0;
        }
        .nav-link:hover, .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .content-area {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .file-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: white;
            transition: all 0.2s ease;
        }
        .file-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .btn-group-sm .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h4 class="text-white">COSMIC系统</h4>
                    <small class="text-white-50">功能拆解与文档生成</small>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="/upload">
                        <i class="bi bi-cloud-upload me-2"></i>文件管理
                    </a>
                    <a class="nav-link active" href="/decompose">
                        <i class="bi bi-diagram-3 me-2"></i>功能拆解
                    </a>
                    <a class="nav-link" href="/validate">
                        <i class="bi bi-check-circle me-2"></i>检查修复
                    </a>
                    <a class="nav-link" href="/document">
                        <i class="bi bi-file-text me-2"></i>文档生成
                    </a>
                    <a class="nav-link" href="/prompts">
                        <i class="bi bi-pencil-square me-2"></i>提示词管理
                    </a>
                    <a class="nav-link" href="/config">
                        <i class="bi bi-gear me-2"></i>系统配置
                    </a>
                    <a class="nav-link" href="/knowledge">
                        <i class="bi bi-journal-text me-2"></i>知识库管理
                    </a>
                </nav>
            </div>
            
            <!-- 主内容区域 -->
            <div class="col-md-9 col-lg-10 content-area p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-diagram-3 me-2"></i>COSMIC功能拆解</h2>
                    <button class="btn btn-primary" onclick="refreshFiles()">
                        <i class="bi bi-arrow-clockwise me-1"></i>刷新
                    </button>
                </div>
                
                <!-- 拆解配置区域 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">拆解配置</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">选择功能清单文件</label>
                                <select class="form-select" id="decomposeFileSelect">
                                    <option value="">请选择文件...</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">选择提示词文件</label>
                                <select class="form-select" id="promptFileSelect">
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">二级模块选择（可选）</label>
                                <select class="form-select" id="level2ModuleSelect">
                                    <option value="">全部二级模块</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <label class="form-label">三级模块选择（可选）</label>
                                <select class="form-select" id="level3ModuleSelect">
                                    <option value="">全部三级模块</option>
                                </select>
                            </div>
                            <div class="col-md-8 d-flex align-items-end">
                                <button class="btn btn-primary" onclick="startDecompose()">
                                    <i class="bi bi-play-circle me-1"></i>开始拆解
                                </button>
                                <div id="decomposeTimer" class="ms-3 text-info" style="display: none;">
                                    <i class="bi bi-clock me-1"></i>已用时: 00:00
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 拆解结果文件列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">拆解结果文件 (<span id="outputFilesCount">0</span>)</h5>
                    </div>
                    <div class="card-body">
                        <div id="outputFilesList">
                            <!-- 文件列表将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/decompose.js"></script>
</body>
</html>
