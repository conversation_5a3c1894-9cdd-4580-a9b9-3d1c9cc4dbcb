<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档生成 - COSMIC功能拆解系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 8px;
            margin: 2px 0;
        }
        .nav-link:hover, .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .content-area {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 12px;
        }
        .file-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
        }
        .file-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .btn-group-sm .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .status-badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h4 class="text-white">COSMIC系统</h4>
                    <small class="text-white-50">功能拆解与文档生成</small>
                </div>

                <nav class="nav flex-column">
                    <a class="nav-link" href="/upload">
                        <i class="bi bi-cloud-upload me-2"></i>文件管理
                    </a>
                    <a class="nav-link" href="/decompose">
                        <i class="bi bi-diagram-3 me-2"></i>功能拆解
                    </a>
                    <a class="nav-link" href="/validate">
                        <i class="bi bi-check-circle me-2"></i>检查修复
                    </a>
                    <a class="nav-link active" href="/document">
                        <i class="bi bi-file-text me-2"></i>文档生成
                    </a>
                    <a class="nav-link" href="/prompts">
                        <i class="bi bi-pencil-square me-2"></i>提示词管理
                    </a>
                    <a class="nav-link" href="/config">
                        <i class="bi bi-gear me-2"></i>系统配置
                    </a>
                    <a class="nav-link" href="/knowledge">
                        <i class="bi bi-journal-text me-2"></i>知识库管理
                    </a>

                </nav>
            </div>

            <!-- 主内容区域 -->
            <div class="col-md-9 col-lg-10 content-area p-4">
                <!-- 文档生成区域 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-file-text me-2"></i>文档生成</h2>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">生成Markdown文档</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">选择拆解结果文件</label>
                                <select class="form-select" id="documentFileSelect">
                                    <option value="">请选择文件...</option>
                                    {% for file in output_files %}
                                    <option value="{{ file.name }}">{{ file.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button class="btn btn-success w-100" onclick="startGenerateDocument()">
                                    <i class="bi bi-file-earmark-text me-1"></i>生成文档
                                </button>
                            </div>
                        </div>
                        <div id="documentTimer" class="mt-2 text-success" style="display: none;">
                            <i class="bi bi-clock me-1"></i>已用时: 00:00
                        </div>
                    </div>
                </div>

                <!-- Markdown文档列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">生成的文档 ({{ word_files|length }})</h5>
                    </div>
                    <div class="card-body">
                        <div id="wordFilesList">
                            {% for file in word_files %}
                            <div class="file-item" data-filename="{{ file.name }}">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h6 class="mb-1">{{ file.name }}</h6>
                                        <small class="text-muted">
                                            {{ "%.2f"|format(file.size/1024/1024) }} MB | {{ file.modified_str }}
                                        </small>
                                    </div>
                                    <div class="col-md-3">
                                        {% if file.name.endswith('.md') %}
                                        <span class="badge bg-primary status-badge">Markdown</span>
                                        {% else %}
                                        <span class="badge bg-info status-badge">Word</span>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="previewFile('words', '{{ file.name }}')">
                                                <i class="bi bi-eye"></i> 预览
                                            </button>
                                            {% if file.name.endswith('.md') %}
                                            <button class="btn btn-outline-info" onclick="convertToWord('{{ file.name }}')">
                                                <i class="bi bi-file-word"></i> 转Word
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-outline-success" onclick="downloadFile('words', '{{ file.name }}')">
                                                <i class="bi bi-download"></i> 下载
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteFile('words', '{{ file.name }}')">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}

                            {% if not word_files %}
                            <div class="text-center text-muted py-4">
                                <i class="bi bi-inbox display-4"></i>
                                <p class="mt-2">暂无生成的文档</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/common.js"></script>
</body>
</html>
